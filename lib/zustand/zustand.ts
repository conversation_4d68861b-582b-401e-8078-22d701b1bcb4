import { create } from "zustand";
import { AuthChangeEvent, Session } from "@supabase/supabase-js";
import { createClient } from "../supabase/client";

interface User {
  id: string;
  userName: string;
  superbaseId: string;
  email: string;
  role: string;
  isPaidUser: boolean;
  isAdmin: boolean;
  credit: number;
  hasPreference: boolean;
}

interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isInitialized: boolean;
  setUser: (user: User | null) => void;
  setIsLoading: (isLoading: boolean) => void;
  getUserFromServer: (user: Session["user"] | null) => Promise<void>;
  getUserWithoutSession(): Promise<void>;
  setIsAuthenticated: (isAuthenticated: boolean) => void;
  updateUserCredit: (newCredit: number) => void;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  isLoading: true,
  isAuthenticated: false,
  isInitialized: false,
  setUser: (user) => set({ user }),
  setIsLoading: (isLoading) => set({ isLoading }),
  setIsAuthenticated: (isAuthenticated) => set({ isAuthenticated }),
  updateUserCredit: (newCredit) =>
    set((state) => ({
      user: state.user ? { ...state.user, credit: newCredit } : null,
    })),

  getUserWithoutSession: async () => {
    const state = get();

    // Prevent multiple simultaneous calls
    if (state.isLoading && state.isInitialized) {
      console.log("Auth check already in progress, skipping");
      return;
    }

    console.log("Getting user without session");
    set({ isLoading: true });
    const supabase = createClient();
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session?.user.email) {
      console.log("No authenticated user found");
      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        isInitialized: true,
      });
      return;
    }
    try {
      const response = await fetch("/api/getUserFromServer", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: session?.user?.email,
        }),
      });
      if (!response.ok) {
        throw new Error(`Failed to fetch user data: ${response.statusText}`);
      }
      const userData = await response.json();
      set({
        user: userData,
        isAuthenticated: true,
        isLoading: false,
        isInitialized: true,
      });
    } catch (error) {
      console.log("Error checking user:", error);
      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        isInitialized: true,
      });
    }
  },

  getUserFromServer: async (user: Session["user"] | null) => {
    set({ isLoading: true });
    try {
      const supabaseUser = user;
      if (!supabaseUser || !supabaseUser.email) {
        console.log("No authenticated user found");
        set({ user: null, isAuthenticated: false });
        return;
      }
      const response = await fetch("/api/getUserFromServer", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: supabaseUser.email,
        }),
      });
      if (!response.ok) {
        throw new Error(`Failed to fetch user data: ${response.statusText}`);
      }

      const userData = await response.json();
      set({
        user: userData,
        isAuthenticated: true,
      });
    } catch (error) {
      console.log("Error checking user:", error);
      set({
        user: null,
        isAuthenticated: false,
      });
    }

    set({ isLoading: false });
  },

  clearUser: () => {
    set({ user: null });
  },
}));
