"use client";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@nextui-org/react";
import { signOut } from "../component/NavbarComponents/action";
import { useAuthStore } from "@/lib/zustand/zustand";
import { Divider } from "@nextui-org/react";
import { useEffect, useRef } from "react";
import { useFormState } from "react-dom";
import toast from "react-hot-toast";
import Link from "next/link";

export default function Page() {
  const router = useRouter();
  const [state, formAction] = useFormState(signOut, null);
  const formRef = useRef<HTMLFormElement>(null);
  const { user, setUser } = useAuthStore();

  console.log(user);

  useEffect(() => {
    if (state?.error) {
      toast.error(state.error);
      formRef.current?.reset();
    }
    if (state?.success) {
      toast.success(state.success);
      setUser(null);
      setTimeout(() => {
        window.location.href = "/";
      }, 500);
      formRef.current?.reset();
    }
  }, [state, router, user, setUser]);

  const subscriptionPlan: any = {
    USER: "Free",
    PAID_USER: "Premium",
    ADMIN: "Admin",
  };

  const handleSignOut = () => {
    formAction();
  };
  // const [isEdit, setEdit] = useState(false)

  if (!user) {
    // This should theoretically never happen due to middleware,
    // but it's a good fail-safe and satisfies TypeScript
    router.push("/signin");
    return null;
  }

  return (
    <div className="w-full h-full bg-[#132435] !text-white p-12">
      <div className="xl:w-11/12 flex flex-col m-auto">
        <div className=" w-full">
          <h1 className="flex flex-row w-full  text-[40px] font-medium mb-4">
            {" "}
            My Account{" "}
          </h1>
          {/* {isEdit ?
                    <form action={updateUsername.bind(null, id)} className="mt-2">
                        <h2 className="text-2xl font-semibold">Update username</h2>
                        <Input className="mb-4" type="username" name="username" id="username" variant="bordered" label="username" placeholder="Enter your username" />
                        <Button variant="solid" className="bg-[#4B7E68] text-white" type="submit">Update</Button>
                    </form>
                    :
                  
                } */}
          <div>
            <div className="text-3xl font-normal text-white mb-4">
              Hi, {user.userName}{" "}
            </div>
            {/* <p className="cursor-pointer text-[#88D84D]" onClick={() => setEdit(true)}>Change Username</p> */}
          </div>
        </div>
        <Divider className="bg-white text-white my-9" />
        <div>
          <div className="flex flex-row w-full text-white text-[22px] font-semibold  mb-4">
            {" "}
            Your Subscription Plan
          </div>
          <div className="text-2xl mb-4"> {subscriptionPlan[user.role]} </div>
          <div className="flex gap-4">
            {/* {subscriptionPlan[user.role] !== "Premium" && (
              <Button
                variant="solid"
                className="px-5 py-3 rounded-full shadow-md  bg-[#88D84D] text-white"
                onClick={() => router.push("/subscription")}
              >
                Get Premium
              </Button>
            )} */}
          </div>
        </div>
        <Divider className="bg-white text-white my-9" />
        <div className="flex flex-col">
          <div className="flex flex-row w-full text-white text-[22px] font-semibold mb-4">
            Login Information
          </div>
          <div>Email : {user.email}</div>
          {/* <a className="text-[#4B7E68]" href="">Change Email</a> */}
          <a className="text-[#88D84D]" href="/send-reset-email">
            Change Password
          </a>
        </div>
        <Divider className="bg-white text-white my-9" />
        <div>
          <div className="flex flex-row w-full text-white text-[22px] font-semibold  mb-4">
            Account
          </div>
          <p
            className="cursor-pointer text-[#88D84D] mb-4"
            onClick={() => handleSignOut()}
          >
            Sign Out
          </p>
          <div>
            To update your subscription,{" "}
            <Link
              className="cursor-pointer text-[#88D84D]"
              href="/subscription"
            >
              {" "}
              Click Here
            </Link>
          </div>
          {/* <div>To delete your account, <a className="cursor-pointer text-[#88D84D]" href="/contact-us"> contact us</a></div> */}
        </div>
      </div>
    </div>
  );
}
