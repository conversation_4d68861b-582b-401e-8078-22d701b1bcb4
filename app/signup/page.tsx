"use client";

import { Input } from "@nextui-org/react";
import { signUp } from "./action";
import PasswordInput from "@/app/component/General/PasswordInput/HiddenInput";
import { useFormState, useFormStatus } from "react-dom";
import { useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { useAuthStore } from "@/lib/zustand/zustand";
import { trackSignUp } from "@/lib/branch";
import GoogleButton from "react-google-button";
import { handleAppleSignIn } from "../component/AppleSigninAction";
import { handleGoogleSignIn } from "../component/GoogleSigninButton";

function SubmitButton() {
  const { pending } = useFormStatus();
  return (
    <button
      className="w-full bg-[#88D84D] text-black font-semibold py-2 rounded-lg hover:bg-[#7BC43D] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
      type="submit"
      disabled={pending}
    >
      {pending ? "Signing up..." : "Sign up"}
    </button>
  );
}

export default function Page() {
  const [state, formAction] = useFormState(signUp, null);
  const formRef = useRef<HTMLFormElement>(null);
  const router = useRouter();
  const getUserWithoutSession = useAuthStore(
    (state) => state.getUserWithoutSession,
  );

  useEffect(() => {
    if (state?.error) {
      toast.error(state.error);
      formRef.current?.reset();
    }
    if (state?.success) {
      toast.success(state.success);
      getUserWithoutSession();
      router.push(state.redirectUrl || "/homepage");
      trackSignUp(state?.userId);
      formRef.current?.reset();
    }
  }, [state, router, getUserWithoutSession]);

  return (
    <div className="min-h-screen bg-[#132435] text-white flex flex-col">
      <div className="flex flex-col items-center justify-start pt-20 flex-grow px-4">
        <div className="w-full max-w-2xl bg-[#132435] rounded-lg  p-8 mt-10">
          <h1 className="text-2xl font-semibold text-center mb-2">
            Create an Account
          </h1>
          <p className="text-sm text-center text-gray-300 mb-6">
            Download executive-ready reports with citations and trends.
          </p>

          <form
            ref={formRef}
            action={formAction}
            className="w-full contact-form"
          >
            <Input
              className="mb-4"
              type="email"
              name="email"
              id="email"
              variant="bordered"
              label="Email"
              placeholder="Enter your email"
              required
              classNames={{
                inputWrapper: "bg-[#0D1B2A] text-white",
                input: "text-white",
              }}
            />

            <PasswordInput
              placeholder="Enter your password"
              label="Password"
              name="password"
            />

            <SubmitButton />

            {/* OR Divider */}
            <div className="w-full my-6 flex items-center">
              <div className="flex-grow h-px bg-gray-600"></div>
              <span className="px-4 text-sm text-gray-400">
                or continue with
              </span>
              <div className="flex-grow h-px bg-gray-600"></div>
            </div>
            <div className="w-full flex gap-4 items-start justify-between">
              <GoogleButton className={"w-full"} onClick={handleGoogleSignIn} />
              <div
                onClick={handleAppleSignIn}
                className="bg-black text-white h-[50px] w-[240px] cursor-pointer hover:bg-gray-800 transition-colors inline-flex items-center justify-center rounded-lg font-medium"
              >
                Continue with Apple
              </div>
            </div>

            {/* Footer Links */}
            <div className="mt-6 text-sm text-center space-y-2 flex items-end flex-col">
              <a
                className="text-[#88D84D] hover:underline block"
                href="/signin"
              >
                Already have an account? Sign in here
              </a>
              <a
                className="text-[#88D84D] hover:underline block"
                href="/send-reset-email"
              >
                Forgot your password? Reset here
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
