import React, { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";

import { Lock, ChevronDown } from "lucide-react";
import { searchUniversities, searchJournals } from "../constants/openAlexData";

interface QueryData {
  count: string;
  type: string;
  keyword: string;
  timeWindow: string; // in months
  yearRange?: {
    start: number;
    end: number;
  };
  filters?: {
    journal?: string;
    affiliation?: string;
    author?: string;
  };
}

interface QueryBuilderProps {
  onQueryComplete: (query: QueryData) => void;
}

const QueryBuilder = ({ onQueryComplete }: QueryBuilderProps) => {
  const [step, setStep] = useState(1);
  const [selectedCount, setSelectedCount] = useState("");
  const [selectedType, setSelectedType] = useState("");
  const [keyword, setKeyword] = useState("");
  const [selectedTimeWindow, setSelectedTimeWindow] = useState("");

  const [yearRange, setYearRange] = useState([2015, 2025]);
  const [startYearInput, setStartYearInput] = useState("2015");
  const [endYearInput, setEndYearInput] = useState("2025");
  const [useYearRange, setUseYearRange] = useState(false);

  // Filter state
  const [journalFilter, setJournalFilter] = useState("");
  const [affiliationFilter, setAffiliationFilter] = useState("");
  const [authorFilter, setAuthorFilter] = useState("");

  // Autocomplete state for institution
  const [showInstitutionSuggestions, setShowInstitutionSuggestions] =
    useState(false);
  const [institutionSuggestions, setInstitutionSuggestions] = useState<
    string[]
  >([]);
  const institutionInputRef = useRef<HTMLInputElement>(null);
  const institutionDropdownRef = useRef<HTMLDivElement>(null);

  // Autocomplete state for journal
  const [showJournalSuggestions, setShowJournalSuggestions] = useState(false);
  const [journalSuggestions, setJournalSuggestions] = useState<string[]>([]);
  const journalInputRef = useRef<HTMLInputElement>(null);
  const journalDropdownRef = useRef<HTMLDivElement>(null);

  const countOptions = [
    { value: "10", label: "10", locked: false },
    { value: "50", label: "50", locked: false },
    { value: "100", label: "100", locked: false },
    { value: "1000", label: "1000", locked: false },
  ];
  const typeOptions = [
    { value: "authors", label: "Researchers", locked: false },
    { value: "papers", label: "Research Papers", locked: false },
    { value: "journals", label: "Journals", locked: true },
    { value: "institutions", label: "Institutions", locked: true },
  ];

  const timeWindowOptions = [
    { value: "1", label: "1 Month" },
    { value: "3", label: "3 Months" },
    { value: "6", label: "6 Months" },
    { value: "12", label: "12 Months" },
  ];

  // Example research keywords (can be customized by user)
  const exampleKeywords = [
    "artificial intelligence",
    "machine learning",
    "climate change",
    "ocean acidification",
    "marine plastic pollution",
    "quantum computing",
    "gene therapy",
    "renewable energy",
  ];

  const handleCountSelect = (count: string) => {
    setSelectedCount(count);
    setStep(2);
  };

  const handleTypeSelect = (type: string) => {
    setSelectedType(type);
    setStep(3); // Go to time window step
  };

  const handleTimeWindowSelect = (timeWindow: string) => {
    setSelectedTimeWindow(timeWindow);
    setUseYearRange(false); // Reset year range mode
    setStep(4); // Go to keyword step
  };

  const handleYearRangeSubmit = () => {
    setUseYearRange(true);
    setSelectedTimeWindow(""); // Clear month-based selection
    setStep(4); // Go to keyword step
  };

  const handleYearRangeChange = (newRange: number[]) => {
    setYearRange(newRange);
    setStartYearInput(newRange[0].toString());
    setEndYearInput(newRange[1].toString());
  };

  const handleStartYearInputChange = (value: string) => {
    setStartYearInput(value);
    const numValue = parseInt(value);
    if (!isNaN(numValue) && numValue >= 1975 && numValue <= yearRange[1]) {
      setYearRange([numValue, yearRange[1]]);
    }
  };

  const handleEndYearInputChange = (value: string) => {
    setEndYearInput(value);
    const numValue = parseInt(value);
    if (!isNaN(numValue) && numValue <= 2025 && numValue >= yearRange[0]) {
      setYearRange([yearRange[0], numValue]);
    }
  };

  const handleKeywordSubmit = (selectedKeyword: string) => {
    console.log(
      "🔍 QueryBuilder - handleKeywordSubmit called with:",
      selectedKeyword,
    );
    console.log("🔍 QueryBuilder - selectedType:", selectedType);
    setKeyword(selectedKeyword);
    // Check if we need to show filters step
    if (selectedType === "authors" || selectedType === "papers") {
      console.log("🔍 QueryBuilder - Going to filters step (step 5)");
      setStep(5); // Go to filters step
    } else {
      console.log("🔍 QueryBuilder - Completing query directly");
      completeQuery(selectedKeyword); // Complete directly for other types
    }
  };

  const completeQuery = (finalKeyword?: string) => {
    const keywordToUse = finalKeyword || keyword;

    // Build filters object only if there are active filters
    const filters: {
      journal?: string;
      affiliation?: string;
      author?: string;
    } = {};
    if (journalFilter.trim() && journalFilter !== "__all__")
      filters.journal = journalFilter.trim();
    if (affiliationFilter.trim())
      filters.affiliation = affiliationFilter.trim();
    if (authorFilter.trim()) filters.author = authorFilter.trim();

    const finalQuery: QueryData = {
      count: selectedCount,
      type: selectedType,
      keyword: keywordToUse,
      timeWindow: selectedTimeWindow,
      yearRange: useYearRange
        ? { start: yearRange[0], end: yearRange[1] }
        : undefined,
      filters: Object.keys(filters).length > 0 ? filters : undefined,
    };

    // DEBUG: Log the complete query being sent
    console.log("🔍 QueryBuilder - Complete query being sent:", finalQuery);
    console.log("🔍 QueryBuilder - Filters applied:", filters);

    onQueryComplete(finalQuery);
  };

  const handleKeywordSelect = (selectedKeyword: string) => {
    handleKeywordSubmit(selectedKeyword);
  };

  const handleCustomKeywordSubmit = () => {
    if (keyword.trim()) {
      handleKeywordSubmit(keyword.trim());
    }
  };

  // Institution autocomplete handlers
  const handleInstitutionInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const value = e.target.value;
    console.log("🔍 Institution input changed:", value);
    setAffiliationFilter(value);

    if (value.trim()) {
      const suggestions = searchUniversities(value).slice(0, 10); // Limit to 10 suggestions
      console.log("🔍 Found suggestions:", suggestions);
      setInstitutionSuggestions(suggestions);
      setShowInstitutionSuggestions(true);
      console.log("🔍 Show suggestions set to true");
    } else {
      setShowInstitutionSuggestions(false);
      console.log("🔍 Show suggestions set to false (empty input)");
    }
  };

  const handleInstitutionSuggestionClick = (suggestion: string) => {
    setAffiliationFilter(suggestion);
    setShowInstitutionSuggestions(false);
  };

  const handleInstitutionInputFocus = () => {
    console.log("🔍 Input focused, current value:", affiliationFilter);
    if (affiliationFilter.trim()) {
      const suggestions = searchUniversities(affiliationFilter).slice(0, 10);
      console.log("🔍 Focus suggestions:", suggestions);
      setInstitutionSuggestions(suggestions);
      setShowInstitutionSuggestions(true);
    } else {
      // Show some default suggestions when focused with empty input
      const defaultSuggestions = [
        "Harvard University",
        "Stanford University",
        "MIT",
        "University of California, Berkeley",
        "Yale University",
      ];
      console.log("🔍 Showing default suggestions on focus");
      setInstitutionSuggestions(defaultSuggestions);
      setShowInstitutionSuggestions(true);
    }
  };

  const handleInstitutionInputBlur = () => {
    // Delay hiding to allow click on suggestions
    console.log("🔍 Input blur - hiding suggestions in 200ms");
    setTimeout(() => {
      console.log("🔍 Hiding suggestions after blur timeout");
      setShowInstitutionSuggestions(false);
    }, 200);
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Handle institution suggestions
      if (
        institutionDropdownRef.current &&
        !institutionDropdownRef.current.contains(event.target as Node) &&
        institutionInputRef.current &&
        !institutionInputRef.current.contains(event.target as Node)
      ) {
        setShowInstitutionSuggestions(false);
      }

      // Handle journal suggestions
      if (
        journalDropdownRef.current &&
        !journalDropdownRef.current.contains(event.target as Node) &&
        journalInputRef.current &&
        !journalInputRef.current.contains(event.target as Node)
      ) {
        setShowJournalSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Journal autocomplete handlers
  const handleJournalInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    console.log("🔍 Journal input changed:", value);
    setJournalFilter(value);

    if (value.trim()) {
      const suggestions = searchJournals(value).slice(0, 10); // Limit to 10 suggestions
      console.log("🔍 Found journal suggestions:", suggestions);
      setJournalSuggestions(suggestions);
      setShowJournalSuggestions(true);
      console.log("🔍 Show journal suggestions set to true");
    } else {
      setShowJournalSuggestions(false);
      console.log("🔍 Show journal suggestions set to false (empty input)");
    }
  };

  const handleJournalSuggestionClick = (suggestion: string) => {
    setJournalFilter(suggestion);
    setShowJournalSuggestions(false);
  };

  const handleJournalInputFocus = () => {
    console.log("🔍 Journal input focused, current value:", journalFilter);
    if (journalFilter.trim()) {
      const suggestions = searchJournals(journalFilter).slice(0, 10);
      console.log("🔍 Journal focus suggestions:", suggestions);
      setJournalSuggestions(suggestions);
      setShowJournalSuggestions(true);
    } else {
      // Show some default journal suggestions when focused with empty input
      const defaultSuggestions = [
        "Nature",
        "Science",
        "Cell",
        "The Lancet",
        "New England Journal of Medicine",
      ];
      console.log("🔍 Showing default journal suggestions on focus");
      setJournalSuggestions(defaultSuggestions);
      setShowJournalSuggestions(true);
    }
  };

  const handleJournalInputBlur = () => {
    // Delay hiding to allow click on suggestions
    console.log("🔍 Journal input blur - hiding suggestions in 200ms");
    setTimeout(() => {
      console.log("🔍 Hiding journal suggestions after blur timeout");
      setShowJournalSuggestions(false);
    }, 200);
  };

  // Test the search function on component mount
  useEffect(() => {
    console.log("🔍 Testing searchUniversities function:");
    const testResults = searchUniversities("stanford");
    console.log("🔍 Search for 'stanford':", testResults);
    const testResults2 = searchUniversities("harvard");
    console.log("🔍 Search for 'harvard':", testResults2);

    console.log("🔍 Testing searchJournals function:");
    const journalResults = searchJournals("nature");
    console.log("🔍 Search for 'nature':", journalResults);
    const journalResults2 = searchJournals("science");
    console.log("🔍 Search for 'science':", journalResults2);
  }, []);

  const getCurrentSentence = () => {
    let sentence = "Give me top";
    if (selectedCount) sentence += ` ${selectedCount}`;
    if (selectedType) sentence += ` ${selectedType}`;
    if (useYearRange) {
      sentence += ` (${yearRange[0]}-${yearRange[1]} analysis)`;
    } else if (selectedTimeWindow) {
      sentence += ` (${selectedTimeWindow} month analysis)`;
    }
    if (step >= 4) sentence += " in";
    if (keyword) sentence += ` "${keyword}"`;
    return sentence;
  };

  return (
    <div
      className={`min-h-screen bg-white flex flex-col items-center px-8 py-8 ${step === 5 ? "justify-start pt-8" : "justify-center"}`}
    >
      <div className="max-w-2xl w-full text-center">
        <h1 className="text-6xl font-light text-gray-900 mb-4">Outread</h1>
        <p className="text-xl text-gray-500 font-light mb-12">
          Ask what's possible.
        </p>

        {/* Current sentence display */}
        <div className="mb-12">
          <p className="text-2xl font-light text-gray-800">
            {getCurrentSentence()}
            <span className="animate-pulse">|</span>
          </p>
        </div>

        {/* Step 1: Select count */}
        {step === 1 && (
          <div className="space-y-6">
            <h2 className="text-xl font-medium text-gray-700 mb-6">
              How many results would you like?
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {countOptions.map((count) => (
                <Button
                  key={count.value}
                  variant="outline"
                  onClick={() =>
                    count.locked ? undefined : handleCountSelect(count.value)
                  }
                  disabled={count.locked}
                  className={`h-16 text-lg border-2 relative ${
                    count.locked
                      ? "bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed"
                      : "hover:bg-gray-100 hover:border-gray-300"
                  }`}
                >
                  {count.locked && (
                    <Lock className="w-4 h-4 absolute top-2 right-2 text-gray-400" />
                  )}
                  {count.label}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Step 2: Select type */}
        {step === 2 && (
          <div className="space-y-6">
            <h2 className="text-xl font-medium text-gray-700 mb-6">
              What type of results?
            </h2>
            <RadioGroup
              value={selectedType}
              onValueChange={handleTypeSelect}
              className="grid grid-cols-1 md:grid-cols-2 gap-4"
            >
              {typeOptions.map((option) => (
                <div
                  key={option.value}
                  className={`flex items-center space-x-3 p-4 border-2 rounded-lg relative ${
                    option.locked
                      ? "bg-gray-100 border-gray-300 cursor-not-allowed"
                      : "hover:bg-gray-50 cursor-pointer"
                  }`}
                >
                  <RadioGroupItem
                    value={option.value}
                    id={option.value}
                    disabled={option.locked}
                    className={option.locked ? "text-gray-400" : ""}
                  />
                  <Label
                    htmlFor={option.value}
                    className={`text-lg flex-1 ${
                      option.locked
                        ? "text-gray-400 cursor-not-allowed"
                        : "cursor-pointer"
                    }`}
                  >
                    {option.label}
                  </Label>
                  {option.locked && <Lock className="w-4 h-4 text-gray-400" />}
                </div>
              ))}
            </RadioGroup>
          </div>
        )}

        {/* Step 3: Select time window */}
        {step === 3 && (
          <div className="space-y-8">
            <h2 className="text-xl font-medium text-gray-700 mb-6">
              Choose a time window for analysis
            </h2>

            {/* Preset time windows */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-600">
                Recent Activity Analysis
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {timeWindowOptions.map((option) => (
                  <Button
                    key={option.value}
                    variant="outline"
                    onClick={() => handleTimeWindowSelect(option.value)}
                    className="h-16 text-center hover:bg-blue-50 border-2 hover:border-blue-300"
                  >
                    <div>
                      <div className="text-lg font-semibold">
                        {option.value}
                      </div>
                      <div className="text-xs text-gray-500">months</div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            {/* Year range option - Locked */}
            {/* Year range option */}
            <div className="space-y-4 border-t pt-6">
              <h3 className="text-lg font-medium text-gray-600">
                Historical Period Analysis
              </h3>
              <div className="bg-gray-50 p-6 rounded-xl space-y-4">
                <div className="text-center mb-4">
                  <p className="text-sm text-gray-600 mb-3">
                    Drag the handles to select your year range
                  </p>
                  <div className="flex items-center justify-center gap-4">
                    <div className="flex flex-col items-center">
                      <span className="text-xs text-gray-500 mb-1">
                        Start Year
                      </span>
                      <Input
                        type="number"
                        value={startYearInput}
                        onChange={(e) =>
                          handleStartYearInputChange(e.target.value)
                        }
                        min={1975}
                        max={2025}
                        className="w-20 text-center text-lg font-medium text-blue-600 bg-blue-50 border-2 border-blue-200"
                      />
                    </div>
                    <div className="flex items-center">
                      <div className="w-8 h-px bg-gray-300"></div>
                      <span className="mx-2 text-gray-500 text-sm">to</span>
                      <div className="w-8 h-px bg-gray-300"></div>
                    </div>
                    <div className="flex flex-col items-center">
                      <span className="text-xs text-gray-500 mb-1">
                        End Year
                      </span>
                      <Input
                        type="number"
                        value={endYearInput}
                        onChange={(e) =>
                          handleEndYearInputChange(e.target.value)
                        }
                        min={1975}
                        max={2025}
                        className="w-20 text-center text-lg font-medium text-blue-600 bg-blue-50 border-2 border-blue-200"
                      />
                    </div>
                  </div>
                </div>

                <div className="px-4 py-2">
                  <Slider
                    value={yearRange}
                    onValueChange={handleYearRangeChange}
                    min={1975}
                    max={2025}
                    step={1}
                    className="w-full"
                    minStepsBetweenThumbs={1}
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-2">
                    <span>1975</span>
                    <span>1985</span>
                    <span>1995</span>
                    <span>2005</span>
                    <span>2015</span>
                    <span>2025</span>
                  </div>
                </div>

                <Button
                  variant="outline"
                  onClick={handleYearRangeSubmit}
                  className="h-12 text-lg hover:bg-green-50 border-2 hover:border-green-300"
                >
                  Use {yearRange[0]}-{yearRange[1]} Period
                </Button>
              </div>
            </div>

            <p className="text-sm text-gray-600 text-center">
              <strong>Recent Activity:</strong> Analyzes citation velocity and
              recent publications.
              <br />
              <strong>Historical Period:</strong> Focuses on publications and
              citations within the specified years.
            </p>
          </div>
        )}

        {/* Step 4: Select keyword */}
        {step === 4 && (
          <div className="space-y-6">
            <h2 className="text-xl font-medium text-gray-700 mb-6">
              Choose a research topic
            </h2>

            {/* Example keywords */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              {exampleKeywords.map((exampleKeyword) => (
                <Button
                  key={exampleKeyword}
                  variant="outline"
                  onClick={() => handleKeywordSelect(exampleKeyword)}
                  className="h-16 text-left p-4 hover:bg-blue-50 border-2 hover:border-blue-300 capitalize"
                >
                  {exampleKeyword}
                </Button>
              ))}
            </div>

            {/* Custom keyword input */}
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <Input
                  type="text"
                  placeholder="Enter your research topic..."
                  value={keyword}
                  onChange={(e) => setKeyword(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && keyword.trim()) {
                      handleCustomKeywordSubmit();
                    }
                  }}
                  className="flex-1 h-12 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-400 focus:ring-0"
                />
                <Button
                  onClick={handleCustomKeywordSubmit}
                  disabled={!keyword.trim()}
                  className="h-12 px-8 bg-blue-600 hover:bg-blue-700 text-white rounded-xl"
                >
                  Search
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Step 5: Optional filters (only for authors and papers) */}
        {step === 5 &&
          (selectedType === "authors" || selectedType === "papers") && (
            <div className="space-y-6 pb-8 w-full max-w-2xl">
              <h2 className="text-xl font-medium text-gray-700 mb-6">
                Optional Filters
              </h2>
              <p className="text-gray-600 mb-6">
                Add filters to narrow down your search (all fields are optional)
              </p>

              <div className="space-y-4">
                {/* Institution/Affiliation filter with autocomplete - for both authors and papers */}
                <div className="relative">
                  <Label
                    htmlFor="affiliation"
                    className="text-sm font-medium text-gray-700 mb-2 block"
                  >
                    Institution/Affiliation
                  </Label>
                  <div className="relative">
                    <Input
                      ref={institutionInputRef}
                      id="affiliation"
                      value={affiliationFilter}
                      onChange={handleInstitutionInputChange}
                      onFocus={handleInstitutionInputFocus}
                      onBlur={handleInstitutionInputBlur}
                      placeholder="e.g., Stanford University, MIT, Harvard"
                      className="w-full h-12 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-400 focus:ring-0 pr-10"
                    />
                    <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />

                    {/* Autocomplete dropdown */}
                    {showInstitutionSuggestions &&
                      institutionSuggestions.length > 0 && (
                        <div
                          ref={institutionDropdownRef}
                          className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto"
                          style={{
                            backgroundColor: "white",
                            border: "2px solid red",
                          }} // Debug styling - Institution
                        >
                          {institutionSuggestions.map((suggestion, index) => {
                            console.log("🔍 Rendering suggestion:", suggestion);
                            return (
                              <div
                                key={`suggestion-${index}-${suggestion}`}
                                className="px-4 py-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                                onClick={() => {
                                  console.log(
                                    "🔍 Suggestion clicked:",
                                    suggestion,
                                  );
                                  handleInstitutionSuggestionClick(suggestion);
                                }}
                              >
                                <div className="text-sm font-medium text-gray-900">
                                  {suggestion}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      )}

                    {/* Debug info */}
                    {process.env.NODE_ENV === "development" && (
                      <div className="text-xs text-gray-400 mt-1">
                        Debug: showSuggestions=
                        {showInstitutionSuggestions.toString()},
                        suggestionsCount={institutionSuggestions.length}
                      </div>
                    )}
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    Filter by institution name or affiliation
                  </p>
                </div>

                {/* Journal filter with autocomplete - only for papers */}
                {selectedType === "papers" && (
                  <div className="relative">
                    <Label
                      htmlFor="journal"
                      className="text-sm font-medium text-gray-700 mb-2 block"
                    >
                      Journal/Publication
                    </Label>
                    <div className="relative">
                      <Input
                        ref={journalInputRef}
                        id="journal"
                        value={journalFilter}
                        onChange={handleJournalInputChange}
                        onFocus={handleJournalInputFocus}
                        onBlur={handleJournalInputBlur}
                        placeholder="e.g., Nature, Science, Cell"
                        className="w-full h-12 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-400 focus:ring-0 pr-10"
                      />
                      <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />

                      {/* Autocomplete dropdown */}
                      {showJournalSuggestions &&
                        journalSuggestions.length > 0 && (
                          <div
                            ref={journalDropdownRef}
                            className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto"
                            style={{
                              backgroundColor: "white",
                              border: "2px solid blue",
                            }} // Debug styling
                          >
                            {journalSuggestions.map((suggestion, index) => {
                              console.log(
                                "🔍 Rendering journal suggestion:",
                                suggestion,
                              );
                              return (
                                <div
                                  key={`journal-suggestion-${index}-${suggestion}`}
                                  className="px-4 py-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                                  onClick={() => {
                                    console.log(
                                      "🔍 Journal suggestion clicked:",
                                      suggestion,
                                    );
                                    handleJournalSuggestionClick(suggestion);
                                  }}
                                >
                                  <div className="text-sm font-medium text-gray-900">
                                    {suggestion}
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        )}

                      {/* Debug info */}
                      {process.env.NODE_ENV === "development" && (
                        <div className="text-xs text-gray-400 mt-1">
                          Debug Journal: showSuggestions=
                          {showJournalSuggestions.toString()}, suggestionsCount=
                          {journalSuggestions.length}
                        </div>
                      )}
                    </div>
                    <p className="text-sm text-gray-500 mt-1">
                      Filter by journal or publication name
                    </p>
                  </div>
                )}

                {/* Author filter - only for papers */}
                {selectedType === "papers" && (
                  <div>
                    <Label
                      htmlFor="author"
                      className="text-sm font-medium text-gray-700 mb-2 block"
                    >
                      Author Name
                    </Label>
                    <Input
                      id="author"
                      value={authorFilter}
                      onChange={(e) => setAuthorFilter(e.target.value)}
                      placeholder="e.g., John Smith, Jane Doe"
                      className="w-full h-12 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-400 focus:ring-0"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      Filter by specific author name
                    </p>
                  </div>
                )}
              </div>

              {/* Action buttons */}
              <div className="flex gap-4 mt-8 mb-8 w-full">
                <div
                  onClick={() => {
                    console.log("Skip filters clicked");
                    completeQuery();
                  }}
                  className="flex-1 h-12 text-lg font-medium rounded-xl transition-colors cursor-pointer flex items-center justify-center"
                  style={{
                    backgroundColor: "white",
                    border: "2px solid #d1d5db",
                    color: "#374151",
                    padding: "12px 24px",
                    borderRadius: "12px",
                    fontSize: "18px",
                    fontWeight: "500",
                    cursor: "pointer",
                    flex: "1",
                    height: "48px",
                    boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = "#f9fafb";
                    e.currentTarget.style.borderColor = "#9ca3af";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = "white";
                    e.currentTarget.style.borderColor = "#d1d5db";
                  }}
                >
                  Skip Filters
                </div>
                <div
                  onClick={() => {
                    console.log("Apply filters clicked");
                    completeQuery();
                  }}
                  className="flex-1 h-12 text-lg font-bold rounded-xl transition-colors cursor-pointer flex items-center justify-center text-white"
                  style={{
                    background:
                      "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                    border: "none",
                    color: "white",
                    padding: "12px 24px",
                    borderRadius: "12px",
                    fontSize: "18px",
                    fontWeight: "bold",
                    cursor: "pointer",
                    flex: "1",
                    height: "48px",
                    boxShadow: "0 2px 4px rgba(16, 185, 129, 0.2)",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background =
                      "linear-gradient(135deg, #059669 0%, #047857 100%)";
                    e.currentTarget.style.boxShadow =
                      "0 4px 8px rgba(16, 185, 129, 0.3)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background =
                      "linear-gradient(135deg, #10b981 0%, #059669 100%)";
                    e.currentTarget.style.boxShadow =
                      "0 2px 4px rgba(16, 185, 129, 0.2)";
                  }}
                >
                  Apply Filters & Search
                </div>
              </div>
            </div>
          )}

        {/* Back button */}
        {step > 1 && (
          <div className="mt-8">
            <Button
              variant="ghost"
              onClick={() => {
                if (step === 2) {
                  setStep(1);
                  setSelectedCount("");
                } else if (step === 3) {
                  setStep(2);
                  setSelectedType("");
                } else if (step === 4) {
                  setStep(3);
                  setKeyword("");
                  setSelectedTimeWindow("");
                  setUseYearRange(false);
                } else if (step === 5) {
                  setStep(4);
                  // Clear filters when going back
                  setJournalFilter("");
                  setAffiliationFilter("");
                  setAuthorFilter("");
                }
              }}
              className="text-gray-500 hover:text-gray-700"
            >
              ← Back
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default QueryBuilder;
