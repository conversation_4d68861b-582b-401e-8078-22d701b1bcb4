import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const TrendingTopics = () => {
  const trendingTopics = [
    { topic: "Large Language Models", papers: 1247, growth: "+23%" },
    { topic: "Computer Vision", papers: 892, growth: "+18%" },
    { topic: "Reinforcement Learning", papers: 634, growth: "+15%" },
    { topic: "Neural Architecture Search", papers: 445, growth: "+31%" },
    { topic: "Federated Learning", papers: 378, growth: "+27%" },
  ];

  const hotInstitutions = [
    { name: "Stanford University", papers: 234, rank: 1 },
    { name: "MIT", papers: 198, rank: 2 },
    { name: "Google Research", papers: 187, rank: 3 },
    { name: "OpenAI", papers: 156, rank: 4 },
    { name: "DeepMind", papers: 134, rank: 5 },
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Trending Research Topics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {trendingTopics.map((item, index) => (
              <div
                key={index}
                className="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
              >
                <div>
                  <p className="font-medium text-gray-900">{item.topic}</p>
                  <p className="text-sm text-gray-600">
                    {item.papers} papers this month
                  </p>
                </div>
                <Badge
                  variant="secondary"
                  className="bg-green-100 text-green-700"
                >
                  {item.growth}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Top Research Institutions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {hotInstitutions.map((item, index) => (
              <div
                key={index}
                className="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    {item.rank}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{item.name}</p>
                    <p className="text-sm text-gray-600">
                      {item.papers} papers this month
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TrendingTopics;
