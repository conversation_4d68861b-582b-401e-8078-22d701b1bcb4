"use client";
import React, { useState, useCallback } from "react";
import ChatInterface from "./ChatInterface";
import ResultsSidebar from "./ResultsSidebar";
import QueryBuilder from "./QueryBuilder";
import DebugModal from "./DebugModal";
import { AuthorData, PaperResult } from "../services/openAlexApi";

interface QueryData {
  count: string;
  type: string;
  keyword: string;
  timeWindow: string;
  yearRange?: {
    start: number;
    end: number;
  };
  filters?: {
    journal?: string;
    affiliation?: string;
    author?: string;
  };
}

const Dashboard = () => {
  const [showChat, setShowChat] = useState(false);
  const [showDebug, setShowDebug] = useState(false);
  const [queryData, setQueryData] = useState<QueryData | null>(null);
  const [authors, setAuthors] = useState<AuthorData[]>([]);
  const [papers, setPapers] = useState<PaperResult[]>([]);

  const handleQueryComplete = (query: QueryData) => {
    console.log("🔍 Dashboard - handleQueryComplete called with:", query);
    setQueryData(query);
    setShowChat(true);
    console.log("🔍 Dashboard - showChat set to true");
  };

  const handleDataUpdate = useCallback(
    (newAuthors: <AUTHORS>
      setAuthors(newAuthors);
      setPapers(newPapers);
    },
    [],
  );

  if (!showChat) {
    return <QueryBuilder onQueryComplete={handleQueryComplete} />;
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="pt-8 pb-6 border-b border-gray-100">
        <div className="text-center">
          <h1 className="text-6xl font-light text-gray-900 mb-4">Outread</h1>
          <p className="text-xl text-gray-500 font-light">
            Ask what{"'"}s possible.
          </p>
        </div>
      </div>

      {/* Debug link */}
      <div className="fixed top-4 right-4 z-50">
        <button
          onClick={() => setShowDebug(true)}
          className="text-xs text-gray-500 hover:text-gray-700 underline bg-white px-2 py-1 rounded shadow"
        >
          Debug AWS
        </button>
      </div>

      <div className="flex min-h-screen w-full">
        <main className="flex-1 min-w-0">
          <ChatInterface
            queryData={queryData}
            authors={authors}
            papers={papers}
          />
        </main>
        <aside className="w-[60%] border-l border-gray-200 bg-white">
          <ResultsSidebar
            queryData={queryData}
            onDataUpdate={handleDataUpdate}
          />
        </aside>
      </div>

      {/* Debug Modal */}
      <DebugModal isOpen={showDebug} onClose={() => setShowDebug(false)} />
    </div>
  );
};

export default Dashboard;
