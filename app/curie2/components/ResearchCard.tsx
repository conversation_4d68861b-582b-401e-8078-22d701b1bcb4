import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, Users, Calendar, TrendingUp } from "lucide-react";

interface ResearchCardProps {
  title: string;
  authors: string[];
  institution: string;
  year: number;
  citations: number;
  abstract: string;
  tags: string[];
  venue: string;
}

const ResearchCard = ({
  title,
  authors,
  institution,
  year,
  citations,
  abstract,
  tags,
  venue,
}: ResearchCardProps) => {
  return (
    <Card className="hover:shadow-2xl transition-all duration-300 cursor-pointer bg-gradient-to-br from-white to-slate-50/60 border-slate-200/60 hover:border-blue-300/60 group hover:scale-[1.02]">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start gap-4">
          <div className="flex items-start space-x-3 flex-1">
            <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center shadow-lg flex-shrink-0 mt-1">
              <FileText className="h-5 w-5 text-white" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-bold text-slate-900 line-clamp-2 leading-tight group-hover:text-blue-600 transition-colors">
                {title}
              </h3>
              <div className="flex items-center text-sm text-blue-600 font-medium mt-2">
                <Users className="h-4 w-4 mr-1" />
                {authors.slice(0, 3).join(", ")}
                {authors.length > 3 && ` +${authors.length - 3} more`}
              </div>
              <div className="flex items-center text-sm text-slate-600 mt-1">
                <span className="font-medium">{institution}</span>
                <span className="mx-2">•</span>
                <span>{venue}</span>
              </div>
            </div>
          </div>
          <div className="text-right text-sm bg-gradient-to-r from-blue-50 to-purple-50 p-3 rounded-xl border border-blue-100 flex-shrink-0">
            <div className="font-bold text-blue-600 flex items-center justify-end">
              <TrendingUp className="h-4 w-4 mr-1" />
              {citations} citations
            </div>
            <div className="text-slate-600 flex items-center justify-end mt-1">
              <Calendar className="h-3 w-3 mr-1" />
              {year}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-sm text-slate-700 line-clamp-3 mb-4 leading-relaxed">
          {abstract}
        </p>
        <div className="flex flex-wrap gap-2 mb-4">
          {tags.map((tag, index) => (
            <Badge
              key={index}
              variant="outline"
              className="text-xs bg-gradient-to-r from-slate-50 to-blue-50 border-slate-300 text-slate-700 hover:from-blue-100 hover:to-purple-100 hover:border-blue-300 transition-all"
            >
              {tag}
            </Badge>
          ))}
        </div>
        <div className="flex justify-between items-center pt-3 border-t border-slate-200/60">
          <Button
            variant="outline"
            size="sm"
            className="border-slate-300 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:border-blue-300 transition-all"
          >
            View Details
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="text-blue-600 hover:bg-blue-50 hover:text-blue-700 transition-all"
          >
            Chat about this paper
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ResearchCard;
