import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { User, MapPin, BookOpen, TrendingUp } from "lucide-react";

interface AuthorCardProps {
  name: string;
  institution: string;
  hIndex: number;
  citations: number;
  papers: number;
  specialization: string[];
  recentWork: string;
  location: string;
}

const AuthorCard = ({
  name,
  institution,
  hIndex,
  citations,
  papers,
  specialization,
  recentWork,
  location,
}: AuthorCardProps) => {
  return (
    <Card className="hover:shadow-2xl transition-all duration-300 cursor-pointer bg-gradient-to-br from-white to-slate-50/60 border-slate-200/60 hover:border-blue-300/60 group hover:scale-[1.02]">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div className="flex items-start space-x-3">
            <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center shadow-lg">
              <User className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-slate-900 group-hover:text-blue-600 transition-colors">
                {name}
              </h3>
              <p className="text-sm text-slate-600 font-medium">
                {institution}
              </p>
              <div className="flex items-center text-xs text-slate-500 mt-1">
                <MapPin className="h-3 w-3 mr-1" />
                {location}
              </div>
            </div>
          </div>
          <div className="text-right text-sm bg-gradient-to-r from-blue-50 to-purple-50 p-3 rounded-xl border border-blue-100">
            <div className="font-bold text-blue-600 text-lg">
              H-Index: {hIndex}
            </div>
            <div className="text-slate-600 flex items-center justify-end mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              {citations.toLocaleString()} citations
            </div>
            <div className="text-slate-500 flex items-center justify-end">
              <BookOpen className="h-3 w-3 mr-1" />
              {papers} papers
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="mb-4">
          <p className="text-xs font-semibold text-slate-700 mb-2 uppercase tracking-wide">
            Specialization
          </p>
          <div className="flex flex-wrap gap-2">
            {specialization.map((spec, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="text-xs bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 border-blue-200 hover:from-blue-200 hover:to-purple-200 transition-colors"
              >
                {spec}
              </Badge>
            ))}
          </div>
        </div>
        <div className="mb-5">
          <p className="text-xs font-semibold text-slate-700 mb-2 uppercase tracking-wide">
            Recent Work
          </p>
          <p className="text-sm text-slate-600 line-clamp-2 leading-relaxed">
            {recentWork}
          </p>
        </div>
        <div className="flex justify-between items-center pt-3 border-t border-slate-200/60">
          <Button
            variant="outline"
            size="sm"
            className="border-slate-300 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:border-blue-300 transition-all"
          >
            View Profile
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="text-blue-600 hover:bg-blue-50 hover:text-blue-700 transition-all"
          >
            Chat with work
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default AuthorCard;
