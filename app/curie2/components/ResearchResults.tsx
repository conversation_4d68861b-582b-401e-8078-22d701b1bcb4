import React from "react";
import AuthorCard from "./AuthorCard";
import ResearchCard from "./ResearchCard";

interface ResearchResultsProps {
  results: any[];
  type: "authors" | "papers" | "institutions";
}

const ResearchResults = ({ results, type }: ResearchResultsProps) => {
  return (
    <div className="space-y-4">
      {type === "authors" &&
        results.map((author, index) => <AuthorCard key={index} {...author} />)}

      {type === "papers" &&
        results.map((paper, index) => <ResearchCard key={index} {...paper} />)}

      {type === "institutions" && (
        <div className="text-center py-8 text-gray-500">
          Institution results coming soon
        </div>
      )}
    </div>
  );
};

export default ResearchResults;
