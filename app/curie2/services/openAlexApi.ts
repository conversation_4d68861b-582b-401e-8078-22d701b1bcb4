// OpenAlex API service for fetching research data and calculating citation metrics

// Common interfaces
export interface SearchFilters {
  journal?: string;
  affiliation?: string;
  author?: string;
}

export interface YearRange {
  start: number;
  end: number;
}

export interface ProgressCallback {
  (progress: { current: number; total: number; stage: string }): void;
}

export interface AuthorData {
  author_id: string;
  author_name: string;
  institutions: string;
  works_count: number;
  lifetime_citations: number;
  avg_citation_velocity: number;
  papers_considered: number;
  top_in_categories: string;
  // Additional metrics for ranking
  i10_index: number;
  h_index: number;
  papers_last_2_years: number;
  top_author_score: number;
  trending_author_score: number;
}

export interface WorkData {
  id: string;
  title: string;
  publication_date: string;
  cited_by_count: number;
  authorships: Array<{
    author: {
      id: string;
      display_name: string;
    };
    institutions: Array<{
      id: string;
      display_name: string;
    }>;
  }>;
  host_venue?: {
    display_name: string;
    id?: string;
  };
  type: string;
  open_access?: {
    is_oa: boolean;
    oa_url?: string;
  };
  doi?: string;
  counts_by_year?: Array<{
    year: number;
    cited_by_count: number;
  }>;
  abstract_inverted_index?: {
    [word: string]: number[];
  };
}

export interface PaperResult {
  id: string;
  title: string;
  authors: string;
  institution?: string; // Primary institution of first author
  allInstitutions?: string; // All institutions from all authors (for filtering)
  publication_date: string;
  cited_by_count: number;
  venue: string;
  type: string;
  citation_velocity: number;
  is_open_access: boolean;
  open_access_url?: string;
  // New fields for ranking and links
  openalex_url: string;
  paper_url?: string; // DOI or best available URL
  citations_last_2_years: number;
  venue_h_index: number;
  altmetric_score: number;
  top_paper_score: number;
  trending_paper_score: number;
  abstract?: string; // Reconstructed from inverted abstract
}

export interface AuthorMetadata {
  display_name: string;
  last_known_institutions: Array<{
    display_name: string;
  }>;
  works_count: number;
  cited_by_count: number;
  summary_stats: {
    h_index: number;
    i10_index: number;
  };
}

const MAILTO = "<EMAIL>";
const PER_PAGE = 200;

// Helper function for delays
const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Search limits configuration
const LIMITS = {
  QUICK: { MAX_PAGES: 5 },
  MODERATE: { MAX_PAGES: 25 },
  EXHAUSTIVE: { MAX_PAGES: 100 },
};

const DEFAULT_LIMIT = LIMITS.EXHAUSTIVE;
const SLEEP_META = 120; // 120ms between requests

// Export the limits for external use
export { LIMITS };
export type FetchLimit = keyof typeof LIMITS;

// Helper function to calculate days since publication
function daysSince(dateIso: string): number {
  const pub = new Date(dateIso);
  const now = new Date();
  const diffTime = now.getTime() - pub.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(diffDays, 1);
}

// Helper function to add delay between requests (removed duplicate - using the one above)

// Reconstruct abstract from inverted abstract index
function reconstructAbstract(abstractInvertedIndex?: {
  [word: string]: number[];
}): string | undefined {
  if (
    !abstractInvertedIndex ||
    Object.keys(abstractInvertedIndex).length === 0
  ) {
    return undefined;
  }

  // Create an array to hold words at their positions
  const wordPositions: { [position: number]: string } = {};
  let maxPosition = 0;

  // Place each word at its positions
  for (const [word, positions] of Object.entries(abstractInvertedIndex)) {
    for (const position of positions) {
      wordPositions[position] = word;
      maxPosition = Math.max(maxPosition, position);
    }
  }

  // Reconstruct the abstract by joining words in order
  const words: string[] = [];
  for (let i = 0; i <= maxPosition; i++) {
    if (wordPositions[i]) {
      words.push(wordPositions[i]);
    }
  }

  return words.join(" ");
}

// Helper function to resolve institution name to OpenAlex ID
async function resolveInstitutionId(
  institutionName: string,
): Promise<string | null> {
  const url = "https://api.openalex.org/institutions";
  const params = new URLSearchParams({
    search: institutionName.trim(),
    "per-page": "1",
    mailto: MAILTO,
  });

  try {
    const response = await fetch(`${url}?${params}`);
    if (!response.ok) {
      console.error(
        `Failed to resolve institution "${institutionName}": ${response.status}`,
      );
      return null;
    }

    const data = await response.json();
    if (data.results && data.results.length > 0) {
      const institutionId = data.results[0].id.split("/").pop(); // Extract ID from URL
      console.log(
        `🔍 Resolved institution "${institutionName}" to ID: ${institutionId}`,
      );
      return institutionId;
    }

    console.log(`🔍 No institution found for "${institutionName}"`);
    return null;
  } catch (error) {
    console.error(`Error resolving institution "${institutionName}":`, error);
    return null;
  }
}

// Helper function to resolve journal/venue name to OpenAlex ID
async function resolveVenueId(venueName: string): Promise<string | null> {
  const url = "https://api.openalex.org/venues";
  const params = new URLSearchParams({
    search: venueName.trim(),
    "per-page": "1",
    mailto: MAILTO,
  });

  try {
    const response = await fetch(`${url}?${params}`);
    if (!response.ok) {
      console.error(
        `Failed to resolve venue "${venueName}": ${response.status}`,
      );
      return null;
    }

    const data = await response.json();
    if (data.results && data.results.length > 0) {
      const venueId = data.results[0].id.split("/").pop(); // Extract ID from URL
      console.log(`🔍 Resolved venue "${venueName}" to ID: ${venueId}`);
      return venueId;
    }

    console.log(`🔍 No venue found for "${venueName}"`);
    return null;
  } catch (error) {
    console.error(`Error resolving venue "${venueName}":`, error);
    return null;
  }
}

// Fetch works for a given term with pagination and optional filters
async function* fetchWorksPages(
  term: string,
  dateFilter: string,
  maxPages: number = DEFAULT_LIMIT.MAX_PAGES,
  filters?: SearchFilters,
) {
  const url = "https://api.openalex.org/works";

  // Resolve filter names to IDs if needed
  let institutionId: string | null = null;
  let venueId: string | null = null;

  if (filters?.affiliation && filters.affiliation.trim()) {
    institutionId = await resolveInstitutionId(filters.affiliation);
  }

  if (filters?.journal && filters.journal.trim()) {
    venueId = await resolveVenueId(filters.journal);
  }

  console.log("Getting number of papers = ${maxPages}");
  for (let page = 1; page <= maxPages; page++) {
    // Build filter string combining date filter with optional affiliation and journal filters
    let combinedFilter = dateFilter;

    if (institutionId) {
      combinedFilter += `,authorships.institutions.id:${institutionId}`;
    }

    if (venueId) {
      combinedFilter += `,host_venue.id:${venueId}`;
    }

    // Log the constructed filter for debugging
    if (page === 1) {
      console.log(`🔍 OpenAlex API filter: ${combinedFilter}`);
      if (filters?.affiliation || filters?.journal) {
        console.log(
          `🔍 Applied filters - Affiliation: "${filters?.affiliation || "none"}" (ID: ${institutionId || "none"}), Journal: "${filters?.journal || "none"}" (ID: ${venueId || "none"})`,
        );
      }
    }

    const params = new URLSearchParams({
      search: term,
      "per-page": PER_PAGE.toString(),
      page: page.toString(),
      filter: combinedFilter,
      mailto: MAILTO,
    });

    try {
      // Add a small delay to avoid rate limiting
      if (page > 1) {
        await sleep(100); // 100ms delay between requests
      }

      const response = await fetch(`${url}?${params}`);
      if (!response.ok) {
        if (response.status === 403) {
          console.error(`OpenAlex API 403 Forbidden. URL: ${url}?${params}`);
          console.error(
            `This might be due to rate limiting or invalid request format`,
          );
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      yield data.results as WorkData[];

      // Add delay between requests
      if (page < maxPages) {
        await sleep(SLEEP_META);
      }
    } catch (error) {
      console.error(`Error fetching page ${page} for term "${term}":`, error);
      break;
    }
  }
}

// Fetch author metadata in batches
async function fetchAuthorsBatch(
  authorIds: string[],
): Promise<Map<string, AuthorMetadata>> {
  const url = "https://api.openalex.org/authors";
  const results = new Map<string, AuthorMetadata>();

  // OpenAlex allows filtering by multiple IDs using the pipe separator
  const idsFilter = authorIds.map((id) => id.split("/").pop()).join("|");
  const params = new URLSearchParams({
    filter: `openalex_id:${idsFilter}`,
    mailto: MAILTO,
    select:
      "id,display_name,last_known_institutions,works_count,cited_by_count,summary_stats",
    "per-page": "200", // Max per page
  });

  try {
    const response = await fetch(`${url}?${params}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Map results by author ID
    for (const author of data.results) {
      results.set(author.id, author as AuthorMetadata);
    }

    return results;
  } catch (error) {
    console.error(`Error fetching author batch:`, error);
    return results;
  }
}

// Fetch venue h-index in batches
async function fetchVenuesBatch(
  venueIds: string[],
): Promise<Map<string, number>> {
  const url = "https://api.openalex.org/venues";
  const results = new Map<string, number>();

  if (venueIds.length === 0) return results;

  // Filter out empty/null venue IDs
  const validIds = venueIds.filter((id) => id && id.trim());
  if (validIds.length === 0) return results;

  const idsFilter = validIds.map((id) => id.split("/").pop()).join("|");
  const params = new URLSearchParams({
    filter: `openalex_id:${idsFilter}`,
    mailto: MAILTO,
    select: "id,summary_stats",
    "per-page": "200",
  });

  try {
    const response = await fetch(`${url}?${params}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Map results by venue ID
    for (const venue of data.results) {
      const hIndex = venue.summary_stats?.h_index || 0;
      results.set(venue.id, hIndex);
    }

    return results;
  } catch (error) {
    console.error(`Error fetching venue batch:`, error);
    return results;
  }
}

// Calculate citations in the specified time window from counts_by_year
function getCitationsInTimeWindow(
  countsByYear: Array<{ year: number; cited_by_count: number }>,
  timeWindowMonths: number,
): number {
  if (!countsByYear || countsByYear.length === 0) return 0;

  const currentYear = new Date().getFullYear();
  const yearsToInclude = Math.ceil(timeWindowMonths / 12);

  // Calculate which years to include based on the time window
  const targetYears: number[] = [];
  for (let i = 0; i < yearsToInclude; i++) {
    targetYears.push(currentYear - i);
  }

  return countsByYear
    .filter((count) => targetYears.includes(count.year))
    .reduce((sum, count) => sum + count.cited_by_count, 0);
}

// Helper function to check if filters are active
function hasActiveFilters(filters?: SearchFilters): boolean {
  return !!(
    filters &&
    ((filters.journal && filters.journal.trim()) ||
      (filters.affiliation && filters.affiliation.trim()) ||
      (filters.author && filters.author.trim()))
  );
}

// Apply client-side filters to author data (only for author name filtering since affiliation is now API-filtered)
function applyAuthorFilters(
  authors: AuthorData[],
  filters?: SearchFilters,
): AuthorData[] {
  if (!filters || !filters.author || !filters.author.trim()) {
    console.log(
      "🔍 applyAuthorFilters - No client-side filters needed, returning all authors",
    );
    return authors;
  }

  console.log(
    `🔍 applyAuthorFilters - Applying client-side author name filter:`,
    filters.author,
  );
  console.log(
    `🔍 applyAuthorFilters - Total authors before filtering:`,
    authors.length,
  );

  const filtered = authors.filter((author, index) => {
    // DEBUG: Log first few authors being processed
    if (index < 3) {
      console.log(`🔍 Processing author ${index}:`, {
        name: author.author_name,
        institutions: author.institutions,
      });
    }

    // Filter by author name (more flexible matching)
    if (filters.author) {
      const filterAuthor = filters.author.toLowerCase().trim();
      const authorName = author.author_name.toLowerCase();

      // Try multiple matching strategies
      const exactMatch = authorName.includes(filterAuthor);
      const wordMatch = filterAuthor
        .split(" ")
        .some((word) => word.length > 2 && authorName.includes(word));

      if (!exactMatch && !wordMatch) {
        if (index < 3) {
          console.log(
            `🔍 Author ${author.author_name} filtered out by name. Looking for "${filterAuthor}" in "${authorName}"`,
          );
        }
        return false;
      } else if (index < 3) {
        console.log(`🔍 Author ${author.author_name} matches name filter`);
      }
    }

    return true;
  });

  console.log(
    `🔍 applyAuthorFilters - Authors after client-side filtering: ${filtered.length}/${authors.length}`,
  );
  return filtered;
}

// Apply client-side filters to paper data (only for author filtering since affiliation/journal are now API-filtered)
function applyPaperFilters(
  papers: PaperResult[],
  filters?: SearchFilters,
): PaperResult[] {
  if (!filters || !filters.author || !filters.author.trim()) {
    console.log(
      "🔍 applyPaperFilters - No client-side filters needed, returning all papers",
    );
    return papers;
  }

  console.log(
    `🔍 applyPaperFilters - Applying client-side author filter:`,
    filters.author,
  );
  console.log(
    `🔍 applyPaperFilters - Total papers before filtering:`,
    papers.length,
  );

  const filtered = papers.filter((paper, index) => {
    // DEBUG: Log first few papers being processed
    if (index < 3) {
      console.log(`🔍 Processing paper ${index}:`, {
        title: paper.title.substring(0, 50) + "...",
        authors: paper.authors.substring(0, 50) + "...",
      });
    }

    // Filter by author (more flexible matching)
    if (filters.author) {
      const filterAuthor = filters.author.toLowerCase().trim();
      const paperAuthors = paper.authors.toLowerCase();

      // Try multiple matching strategies
      const exactMatch = paperAuthors.includes(filterAuthor);
      const wordMatch = filterAuthor
        .split(" ")
        .some((word) => word.length > 2 && paperAuthors.includes(word));

      if (!exactMatch && !wordMatch) {
        if (index < 3) {
          console.log(
            `🔍 Paper filtered out by author. Looking for "${filterAuthor}" in "${paperAuthors}"`,
          );
        }
        return false;
      } else if (index < 3) {
        console.log(`🔍 Paper matches author filter`);
      }
    }

    return true;
  });

  console.log(
    `🔍 applyPaperFilters - Papers after client-side filtering: ${filtered.length}/${papers.length}`,
  );
  return filtered;
}

// Generic retry mechanism for progressive search
async function progressiveSearch<T, C>(
  searchFunction: (
    mode: keyof typeof LIMITS,
  ) => Promise<{ results: T[]; cacheData: C }>,
  topK: number,
  onProgress?: ProgressCallback,
  searchId?: string,
): Promise<{ results: T[]; cacheData: C }> {
  const searchModes: (keyof typeof LIMITS)[] = [
    "QUICK",
    "MODERATE",
    "EXHAUSTIVE",
  ];

  for (let modeIndex = 0; modeIndex < searchModes.length; modeIndex++) {
    const currentMode = searchModes[modeIndex];

    onProgress?.({
      current: 0,
      total: 100,
      stage: `${currentMode} search: Looking for ${topK} results...`,
    });

    try {
      console.log(
        `${searchId ? `[${searchId}] ` : ""}Trying ${currentMode} mode (attempt ${modeIndex + 1}/${searchModes.length})`,
      );

      const response = await searchFunction(currentMode);

      console.log(
        `${currentMode} mode: Got ${response.results.length} results after filtering, need ${topK}`,
      );

      // If we have enough results, return immediately
      if (response.results.length >= topK) {
        console.log(
          `Success! Found enough results (${response.results.length}/${topK}) using ${currentMode} mode`,
        );

        onProgress?.({
          current: 100,
          total: 100,
          stage: `Found ${response.results.length} results using ${currentMode} search.`,
        });

        return {
          results: response.results.slice(0, topK),
          cacheData: response.cacheData,
        };
      }

      // If this is the last mode (EXHAUSTIVE), return whatever we have
      if (currentMode === "EXHAUSTIVE") {
        console.log(
          `Final attempt complete. Returning ${response.results.length} results after EXHAUSTIVE search (wanted ${topK})`,
        );

        onProgress?.({
          current: 100,
          total: 100,
          stage: `Found ${response.results.length} results after exhaustive search.`,
        });

        return {
          results: response.results,
          cacheData: response.cacheData,
        };
      }

      // If we don't have enough results, try the next more intensive mode
      console.log(
        `${currentMode} mode insufficient (${response.results.length}/${topK}), escalating to next search mode`,
      );

      onProgress?.({
        current: 33 * (modeIndex + 1),
        total: 100,
        stage: `Found ${response.results.length}/${topK} with ${currentMode} search. Trying more intensive search...`,
      });

      await sleep(1000); // Brief delay between modes
    } catch (error) {
      console.error(`${currentMode} mode failed:`, error);

      // If this is the last mode, throw the error
      if (currentMode === "EXHAUSTIVE") {
        throw error;
      }

      // Otherwise, try the next mode
      console.log(`Trying next search mode due to error`);
    }
  }

  // This should never be reached
  console.error("🚨 Progressive search failed to complete properly!");
  throw new Error("Progressive search failed to complete properly");
}

// Wrapper function for authors with retry mechanism
export async function fetchAuthorsWithRetry(
  keyword: string,
  topK: number = 100,
  sortBy: SortOption = "top_author_score",
  timeWindowMonths: number = 12,
  onProgress?: ProgressCallback,
  useCache: boolean = true,
  cachedData?: ResearcherDataCache,
  filters?: SearchFilters,
  yearRange?: YearRange,
): Promise<{ authors: AuthorData[]; cacheData: ResearcherDataCache }> {
  console.log(
    `fetchAuthorsWithRetry: hasActiveFilters=${hasActiveFilters(filters)}, filters=`,
    filters,
  );

  const searchFunction = async (mode: keyof typeof LIMITS) => {
    const response = await fetchOceanResearchers(
      keyword,
      topK,
      sortBy,
      timeWindowMonths,
      onProgress,
      useCache,
      cachedData,
      filters,
      yearRange,
      mode,
    );
    return { results: response.authors, cacheData: response.cacheData };
  };

  const result = await progressiveSearch(searchFunction, topK, onProgress);
  return { authors: result.results, cacheData: result.cacheData };
}

// Wrapper function for papers with retry mechanism
export type PaperSortOption =
  | "citation_velocity"
  | "top_paper_score"
  | "trending_paper_score";

export async function fetchPapersWithRetry(
  keyword: string,
  topK: number = 100,
  timeWindowMonths: number = 12,
  onProgress?: ProgressCallback,
  useCache: boolean = true,
  cachedData?: PaperDataCache,
  filters?: SearchFilters,
  yearRange?: YearRange,
  _fetchLimit: keyof typeof LIMITS = "MODERATE",
  sortBy: PaperSortOption = "top_paper_score",
): Promise<{ papers: PaperResult[]; cacheData: PaperDataCache }> {
  const searchId = Math.random().toString(36).substring(7);
  console.log(
    `🔄 fetchPapersWithRetry [${searchId}]: STARTING progressive search for ${topK} papers`,
  );
  console.log(
    `🔄 fetchPapersWithRetry [${searchId}]: hasActiveFilters=${hasActiveFilters(filters)}, filters=`,
    filters,
  );

  const searchFunction = async (mode: keyof typeof LIMITS) => {
    const response = await fetchResearchPapers(
      keyword,
      topK,
      timeWindowMonths,
      onProgress,
      useCache,
      cachedData,
      filters,
      yearRange,
      mode,
      sortBy,
    );
    return { results: response.papers, cacheData: response.cacheData };
  };

  const result = await progressiveSearch(
    searchFunction,
    topK,
    onProgress,
    searchId,
  );
  return { papers: result.results, cacheData: result.cacheData };
}

export type SortOption =
  | "citation_velocity"
  | "top_author_score"
  | "trending_author_score";

export interface ResearcherDataCache {
  keyword: string;
  allAuthors: <AUTHORS>
  lastFetched: Date;
}

export interface PaperDataCache {
  keyword: string;
  allPapers: PaperResult[];
  lastFetched: Date;
}

// Main function to fetch researchers
export async function fetchOceanResearchers(
  keyword: string,
  topK: number = 100,
  sortBy: SortOption = "top_author_score",
  timeWindowMonths: number = 12,
  onProgress?: ProgressCallback,
  useCache: boolean = true,
  cachedData?: ResearcherDataCache,
  filters?: SearchFilters,
  yearRange?: YearRange,
  fetchLimit: keyof typeof LIMITS = "MODERATE",
): Promise<{ authors: AuthorData[]; cacheData: ResearcherDataCache }> {
  // DEBUG: Log function entry and filters
  console.log("🔍 fetchOceanResearchers - Called with filters:", filters);
  console.log("🔍 fetchOceanResearchers - Keyword:", keyword, "TopK:", topK);
  // Check if we can use cached data for the same keyword
  if (useCache && cachedData && cachedData.keyword === keyword) {
    onProgress?.({ current: 100, total: 100, stage: "Using cached data..." });

    // Apply filters to cached data
    const filteredAuthors = applyAuthorFilters(
      [...cachedData.allAuthors],
      filters,
    );

    // Sort the filtered data according to the requested sort option
    const sortedResults = filteredAuthors
      .sort((a, b) => {
        switch (sortBy) {
          case "trending_author_score":
            return b.trending_author_score - a.trending_author_score;
          case "citation_velocity":
            return b.avg_citation_velocity - a.avg_citation_velocity;
          case "top_author_score":
          default:
            return b.top_author_score - a.top_author_score;
        }
      })
      .slice(0, topK);

    return {
      authors: sortedResults,
      cacheData: cachedData,
    };
  }

  // Create date filter based on yearRange or timeWindowMonths
  let dateFilter: string;
  if (yearRange) {
    // Use year range for filtering
    dateFilter = `from_publication_date:${yearRange.start}-01-01,to_publication_date:${yearRange.end}-12-31`;
  } else {
    // Use time window in months
    const cutoffDate = new Date();
    cutoffDate.setMonth(cutoffDate.getMonth() - timeWindowMonths);
    const cutoffIso = cutoffDate.toISOString().split("T")[0];
    dateFilter = `from_publication_date:${cutoffIso}`;
  }

  // Store author velocities: author_id -> [velocities]
  const authorVelocities: Map<string, number[]> = new Map();

  // Global dedup store: author_id -> {work_id: velocity}
  const authorWorkVels: Map<string, Map<string, number>> = new Map();

  onProgress?.({
    current: 0,
    total: 100,
    stage: "Fetching research papers...",
  });

  let processedPages = 0;

  // Check if we have active filters that require exhaustive search
  const hasActiveFiltersForSearch = hasActiveFilters(filters);

  // Use EXHAUSTIVE mode when filters are present to ensure we find all matches
  const searchLimit = hasActiveFiltersForSearch ? "EXHAUSTIVE" : fetchLimit;
  const actualLimit = LIMITS[searchLimit];
  const totalPages = actualLimit.MAX_PAGES;

  console.log(
    `🔍 fetchOceanResearchers - Using ${searchLimit} search mode (${hasActiveFiltersForSearch ? "EXHAUSTIVE due to active filters" : "normal mode"}). Has active filters:`,
    hasActiveFiltersForSearch,
    "Filters:",
    filters,
  );

  // Fetch works for the keyword
  for await (const works of fetchWorksPages(
    keyword,
    dateFilter,
    actualLimit.MAX_PAGES,
    filters,
  )) {
    processedPages++;
    onProgress?.({
      current: (processedPages / totalPages) * 50,
      total: 100,
      stage: `Processing page ${processedPages} of ${totalPages}...`,
    });

    for (const work of works) {
      if (!work.publication_date || !work.authorships.length) {
        continue;
      }

      const workId = work.id;
      const velocity = work.cited_by_count / daysSince(work.publication_date);

      // Only take the first author (as in the Python script)
      const firstAuthor = work.authorships[0].author.id;

      // Add to keyword-specific list
      if (!authorVelocities.has(firstAuthor)) {
        authorVelocities.set(firstAuthor, []);
      }
      authorVelocities.get(firstAuthor)!.push(velocity);

      // Global dedup by work_id
      if (!authorWorkVels.has(firstAuthor)) {
        authorWorkVels.set(firstAuthor, new Map());
      }
      authorWorkVels.get(firstAuthor)!.set(workId, velocity);
    }
  }

  // Get ALL authors by average citation velocity (don't limit to topK yet)
  const rankedAuthors = Array.from(authorVelocities.entries())
    .map(([authorId, velocities]) => ({
      authorId,
      avgVelocity:
        velocities.reduce((sum, v) => sum + v, 0) / velocities.length,
    }))
    .sort((a, b) => b.avgVelocity - a.avgVelocity);
  // Note: Removed .slice(0, topK) - we'll select topK after calculating all scores

  onProgress?.({
    current: 50,
    total: 100,
    stage: "Fetching author metadata...",
  });

  // Fetch metadata for top authors using batching
  const results: AuthorData[] = [];
  const authorIds = rankedAuthors.map((a) => a.authorId);
  const BATCH_SIZE = 50; // Process authors in batches of 50

  // Process authors in batches
  for (
    let batchStart = 0;
    batchStart < authorIds.length;
    batchStart += BATCH_SIZE
  ) {
    const batchEnd = Math.min(batchStart + BATCH_SIZE, authorIds.length);
    const batchIds = authorIds.slice(batchStart, batchEnd);

    onProgress?.({
      current: 50 + (batchStart / authorIds.length) * 40,
      total: 100,
      stage: `Fetching batch ${Math.floor(batchStart / BATCH_SIZE) + 1} of ${Math.ceil(authorIds.length / BATCH_SIZE)}...`,
    });

    try {
      // Fetch author metadata in batch
      const authorMetadataMap = await fetchAuthorsBatch(batchIds);

      // Process each author in the batch
      for (let i = batchStart; i < batchEnd; i++) {
        const { authorId, avgVelocity } = rankedAuthors[i];
        const metadata = authorMetadataMap.get(authorId);

        if (!metadata) {
          console.warn(`No metadata found for author ${authorId}`);
          continue;
        }

        const institutions = metadata.last_known_institutions?.length
          ? metadata.last_known_institutions
              .map((inst) => inst.display_name)
              .join("; ")
          : "—";

        const authorWorkMap = authorWorkVels.get(authorId);
        const papersConsidered = authorWorkMap ? authorWorkMap.size : 0;

        // Extract metrics with fallbacks
        const hIndex = metadata.summary_stats?.h_index || 0;
        const i10Index = metadata.summary_stats?.i10_index || 0;
        const totalCitations = metadata.cited_by_count || 0;

        // For now, we'll estimate recent papers count based on total works and career length
        // This is faster than making individual API calls for each author
        const estimatedRecentPapers = Math.max(
          1,
          Math.floor(metadata.works_count * 0.1),
        ); // Rough estimate

        // Calculate scores
        // Top Author Score = (Total Citations) + 10×(i10-index score) + 5×(Paper Count in past 2 years)
        const topAuthorScore =
          totalCitations + 10 * i10Index + 5 * estimatedRecentPapers;

        // Trending Author Score = Citation Velocity (already calculated as avgVelocity)
        const trendingAuthorScore = avgVelocity;

        results.push({
          author_id: authorId,
          author_name: metadata.display_name,
          institutions,
          works_count: metadata.works_count,
          lifetime_citations: totalCitations,
          avg_citation_velocity: avgVelocity,
          papers_considered: papersConsidered,
          top_in_categories: keyword,
          i10_index: i10Index,
          h_index: hIndex,
          papers_last_2_years: estimatedRecentPapers,
          top_author_score: topAuthorScore,
          trending_author_score: trendingAuthorScore,
        });
      }

      // Add delay between batches
      if (batchEnd < authorIds.length) {
        await sleep(SLEEP_META);
      }
    } catch (error) {
      console.error(`Error processing batch starting at ${batchStart}:`, error);
    }
  }

  // Apply filters to results
  const filteredResults = applyAuthorFilters(results, filters);

  // Update progress with filtering information
  const filterMessage = hasActiveFiltersForSearch
    ? `Found ${filteredResults.length} valid matches out of ${results.length} authors (exhaustive search)`
    : "Complete!";
  onProgress?.({ current: 100, total: 100, stage: filterMessage });

  // Log filtering results for debugging
  if (hasActiveFiltersForSearch) {
    console.log(
      `🔍 Author filtering complete: ${filteredResults.length}/${results.length} matches`,
    );
    if (filteredResults.length > 0) {
      console.log(
        `🔍 Sample matching authors:`,
        filteredResults.slice(0, 3).map((a) => ({
          name: a.author_name,
          institutions: a.institutions,
        })),
      );
    }
  }

  // Sort filtered results based on the selected metric
  const sortedResults = filteredResults.sort((a, b) => {
    switch (sortBy) {
      case "trending_author_score":
        return b.trending_author_score - a.trending_author_score;
      case "citation_velocity":
        return b.avg_citation_velocity - a.avg_citation_velocity;
      case "top_author_score":
      default:
        return b.top_author_score - a.top_author_score;
    }
  });

  // Create cache data with all results (not just top K)
  const cacheData: ResearcherDataCache = {
    keyword,
    allAuthors: <AUTHORS>
    lastFetched: new Date(),
  };

  // Check if we have enough results after filtering
  if (sortedResults.length < topK && filters) {
    onProgress?.({
      current: 95,
      total: 100,
      stage: `Found ${sortedResults.length}/${topK} results after filtering. Consider broadening filters.`,
    });
  }

  return {
    authors: sortedResults.slice(0, topK),
    cacheData,
  };
}

// Function to fetch research papers with enhanced metrics
export async function fetchResearchPapers(
  keyword: string,
  topK: number = 100,
  timeWindowMonths: number = 12,
  onProgress?: ProgressCallback,
  useCache: boolean = true,
  cachedData?: PaperDataCache,
  filters?: SearchFilters,
  yearRange?: YearRange,
  fetchLimit: keyof typeof LIMITS = "MODERATE",
  sortBy: PaperSortOption = "top_paper_score",
): Promise<{ papers: PaperResult[]; cacheData: PaperDataCache }> {
  // DEBUG: Log function entry and filters
  console.log("🔍 fetchResearchPapers - Called with filters:", filters);
  console.log("🔍 fetchResearchPapers - Keyword:", keyword, "TopK:", topK);
  // Check if we can use cached data for the same keyword
  if (useCache && cachedData && cachedData.keyword === keyword) {
    onProgress?.({ current: 100, total: 100, stage: "Using cached data..." });

    // Apply filters to cached data
    const filteredPapers = applyPaperFilters(
      [...cachedData.allPapers],
      filters,
    );

    // Sort the filtered data according to the requested sort option
    const sortedResults = filteredPapers
      .sort((a, b) => {
        switch (sortBy) {
          case "trending_paper_score":
            return b.trending_paper_score - a.trending_paper_score;
          case "citation_velocity":
            return b.citation_velocity - a.citation_velocity;
          case "top_paper_score":
          default:
            return b.top_paper_score - a.top_paper_score;
        }
      })
      .slice(0, topK);

    return {
      papers: sortedResults,
      cacheData: cachedData,
    };
  }

  // Create date filter based on yearRange or timeWindowMonths
  let dateFilter: string;
  if (yearRange) {
    // Use year range for filtering
    dateFilter = `from_publication_date:${yearRange.start}-01-01,to_publication_date:${yearRange.end}-12-31`;
  } else {
    // Use time window in months
    const cutoffDate = new Date();
    cutoffDate.setMonth(cutoffDate.getMonth() - timeWindowMonths);
    const cutoffIso = cutoffDate.toISOString().split("T")[0];
    dateFilter = `from_publication_date:${cutoffIso}`;
  }

  onProgress?.({
    current: 0,
    total: 100,
    stage: "Fetching research papers...",
  });

  const tempResults: Array<{
    work: WorkData;
    velocity: number;
    authorsDisplay: string;
    venue: string;
  }> = [];

  let processedPages = 0;

  // Check if we have active filters that require exhaustive search
  const hasActiveFiltersForSearch = hasActiveFilters(filters);

  // Use the specified fetchLimit (progressive search will handle escalation)
  const searchLimit = fetchLimit;
  const actualLimit = LIMITS[searchLimit];
  const totalPages = actualLimit.MAX_PAGES; // Fetch full limit, not limited by topK

  console.log(
    `🔍 fetchResearchPapers - Using ${searchLimit} search mode (${hasActiveFiltersForSearch ? "will escalate to EXHAUSTIVE due to active filters" : "normal progressive search"}). Has active filters:`,
    hasActiveFiltersForSearch,
    "Filters:",
    filters,
  );

  // Fetch works for the keyword
  for await (const works of fetchWorksPages(
    keyword,
    dateFilter,
    actualLimit.MAX_PAGES,
    filters,
  )) {
    processedPages++;
    onProgress?.({
      current: (processedPages / totalPages) * 50,
      total: 100,
      stage: `Processing page ${processedPages} of ${totalPages}...`,
    });

    for (const work of works) {
      if (!work.publication_date) continue;

      // Calculate citation velocity
      const velocity = work.cited_by_count / daysSince(work.publication_date);

      // Get authors list
      const authors = work.authorships
        .slice(0, 3) // Take first 3 authors
        .map((authorship) => authorship.author.display_name)
        .join(", ");

      const authorsDisplay =
        work.authorships.length > 3
          ? `${authors} et al.`
          : authors || "Unknown";

      // Get venue
      const venue = work.host_venue?.display_name || "Unknown Venue";

      tempResults.push({
        work,
        velocity,
        authorsDisplay,
        venue,
      });

      // Stop if we have enough results
      if (tempResults.length >= topK) {
        break;
      }
    }

    if (tempResults.length >= topK) {
      break;
    }

    // Add delay between requests
    if (processedPages < totalPages) {
      await sleep(SLEEP_META);
    }
  }

  onProgress?.({ current: 50, total: 100, stage: "Fetching venue metrics..." });

  // Get unique venue IDs for batch fetching
  const venueIds = Array.from(
    new Set(
      tempResults
        .map((r) => r.work.host_venue?.id)
        .filter((id): id is string => id !== undefined && id.trim() !== ""),
    ),
  );

  // Fetch venue h-indices in batch
  const venueHIndices = await fetchVenuesBatch(venueIds);

  onProgress?.({ current: 75, total: 100, stage: "Calculating scores..." });

  // Process results with all metrics
  const results: PaperResult[] = tempResults.map((item) => {
    const { work, velocity, authorsDisplay, venue } = item;

    // Calculate citations in the specified time window
    const citationsInTimeWindow = getCitationsInTimeWindow(
      work.counts_by_year || [],
      timeWindowMonths,
    );

    // Get venue h-index
    const venueHIndex = work.host_venue?.id
      ? venueHIndices.get(work.host_venue.id) || 0
      : 0;

    // For now, we'll use a placeholder for Altmetric score since it's not available in OpenAlex
    // In a real implementation, you'd need to call the Altmetric API separately
    const altmetricScore = 0; // Placeholder

    // Calculate scores
    // Top Paper Score = (Total Citations) + (5×Citations in time window) + (10×h-index of Journal)
    const topPaperScore =
      work.cited_by_count + 5 * citationsInTimeWindow + 10 * venueHIndex;

    // Trending Paper Score = (4×Altmetric Score) + (5×Citation Velocity)
    const trendingPaperScore = 4 * altmetricScore + 5 * velocity;

    // Create URLs
    const openalexUrl = work.id;
    const paperUrl = work.doi
      ? `https://doi.org/${work.doi.replace("https://doi.org/", "")}`
      : work.open_access?.oa_url || openalexUrl;

    // Reconstruct abstract from inverted index
    const abstract = reconstructAbstract(work.abstract_inverted_index);

    // Extract primary institution (first author's first institution)
    const primaryInstitution =
      work.authorships?.[0]?.institutions?.[0]?.display_name || undefined;

    // Extract ALL institutions from ALL authors for comprehensive filtering
    const allInstitutions =
      work.authorships
        ?.flatMap(
          (authorship) =>
            authorship.institutions?.map((inst) => inst.display_name) || [],
        )
        .filter(Boolean)
        .join("; ") || "";

    // DEBUG: Log institution extraction for papers with missing institutions
    if (!primaryInstitution && tempResults.indexOf(item) < 5) {
      console.log(`🔍 Missing institution for paper:`, {
        title: work.title.substring(0, 50),
        authorships: work.authorships?.length || 0,
        firstAuthorInstitutions:
          work.authorships?.[0]?.institutions?.length || 0,
        allInstitutions: allInstitutions,
        allAuthorships:
          work.authorships?.map((a) => ({
            author: a.author?.display_name,
            institutions: a.institutions?.map((i) => i.display_name),
          })) || [],
      });
    }

    return {
      id: work.id,
      title: work.title,
      authors: authorsDisplay,
      institution: primaryInstitution,
      allInstitutions: allInstitutions,
      publication_date: work.publication_date,
      cited_by_count: work.cited_by_count,
      venue,
      type: work.type || "article",
      citation_velocity: velocity,
      is_open_access: work.open_access?.is_oa || false,
      open_access_url: work.open_access?.oa_url,
      openalex_url: openalexUrl,
      paper_url: paperUrl,
      citations_last_2_years: citationsInTimeWindow,
      venue_h_index: venueHIndex,
      altmetric_score: altmetricScore,
      top_paper_score: topPaperScore,
      trending_paper_score: trendingPaperScore,
      abstract,
    };
  });

  // Apply filters to results
  const filteredResults = applyPaperFilters(results, filters);

  // Update progress with filtering information
  const filterMessage = hasActiveFiltersForSearch
    ? `Found ${filteredResults.length} valid matches out of ${results.length} papers (exhaustive search)`
    : "Complete!";
  onProgress?.({ current: 100, total: 100, stage: filterMessage });

  // Log filtering results for debugging
  if (hasActiveFiltersForSearch) {
    console.log(
      `🔍 Paper filtering complete: ${filteredResults.length}/${results.length} matches`,
    );
    if (filteredResults.length > 0) {
      console.log(
        `🔍 Sample matching papers:`,
        filteredResults.slice(0, 3).map((p) => ({
          title: p.title.substring(0, 50) + "...",
          venue: p.venue,
          institution: p.institution,
          allInstitutions: p.allInstitutions?.substring(0, 100) + "...",
        })),
      );
    }
  }

  // Sort filtered results based on the selected metric
  const sortedResults = filteredResults.sort((a, b) => {
    switch (sortBy) {
      case "trending_paper_score":
        return b.trending_paper_score - a.trending_paper_score;
      case "citation_velocity":
        return b.citation_velocity - a.citation_velocity;
      case "top_paper_score":
      default:
        return b.top_paper_score - a.top_paper_score;
    }
  });

  // Create cache data with all results (not just top K)
  const cacheData: PaperDataCache = {
    keyword,
    allPapers: results, // Store all papers, not just top K
    lastFetched: new Date(),
  };

  // Check if we have enough results after filtering
  if (sortedResults.length < topK && filters) {
    onProgress?.({
      current: 95,
      total: 100,
      stage: `Found ${sortedResults.length}/${topK} results after filtering. Consider broadening filters.`,
    });
  }

  return {
    papers: sortedResults.slice(0, topK),
    cacheData,
  };
}
