import { AuthorData, PaperResult } from "./openAlexApi";

export interface ChatMessage {
  role: "user" | "assistant";
  content: string;
}

export interface DatasetContext {
  authors?: AuthorData[];
  papers?: PaperResult[];
  queryData?: {
    count: string;
    type: string;
    keyword: string;
    timeWindow: string;
  };
}

export async function sendMessageToClaude(
  messages: ChatMessage[],
  datasetContext: DatasetContext,
  onChunk?: (chunk: string) => void,
): Promise<string> {
  try {
    // Prepare the dataset summary for context
    let datasetSummary = "DATASET CONTEXT:\n";

    if (datasetContext.queryData) {
      const { queryData } = datasetContext;
      datasetSummary += `Query: Top ${queryData.count} ${queryData.type} in "${queryData.keyword}" (${queryData.timeWindow} month analysis)\n\n`;
    }

    if (datasetContext.authors && datasetContext.authors.length > 0) {
      datasetSummary += `AUTHORS DATA (${datasetContext.authors.length} entries):\n`;
      datasetSummary += `Fields: author_name, institutions, h_index, i10_index, papers_last_2_years, top_author_score, trending_author_score, lifetime_citations, avg_citation_velocity\n\n`;

      // Include ALL authors data
      datasetSummary += "COMPLETE AUTHORS LIST:\n";
      datasetContext.authors.forEach((author, idx) => {
        datasetSummary += `${idx + 1}. ${author.author_name} | Institution: ${author.institutions} | H-index: ${author.h_index} | i10-index: ${author.i10_index} | Papers (2y): ${author.papers_last_2_years} | Citations: ${author.lifetime_citations} | Top Score: ${author.top_author_score} | Trending Score: ${author.trending_author_score} | Avg Citation Velocity: ${author.avg_citation_velocity}\n`;
      });
      datasetSummary += "\n";
    }

    if (datasetContext.papers && datasetContext.papers.length > 0) {
      datasetSummary += `PAPERS DATA (${datasetContext.papers.length} entries):\n`;
      datasetSummary += `Fields: title, authors, publication_date, cited_by_count, venue, citation_velocity, top_paper_score, trending_paper_score, venue_h_index, citations_last_2_years, abstract\n\n`;

      // Include ALL papers data with abstracts
      datasetSummary += "COMPLETE PAPERS LIST:\n";
      let papersWithAbstracts = 0;
      datasetContext.papers.forEach((paper, idx) => {
        const year = new Date(paper.publication_date).getFullYear();
        datasetSummary += `${idx + 1}. "${paper.title}"\n`;
        datasetSummary += `   Authors: <AUTHORS>
        datasetSummary += `   Year: ${year} | Citations: ${paper.cited_by_count} | Citations (2y): ${paper.citations_last_2_years}\n`;
        datasetSummary += `   Venue: ${paper.venue} | Venue H-index: ${paper.venue_h_index}\n`;
        datasetSummary += `   Citation Velocity: ${paper.citation_velocity.toFixed(3)} | Top Score: ${paper.top_paper_score} | Trending Score: ${paper.trending_paper_score.toFixed(1)}\n`;

        // Include abstract if available
        if (paper.abstract && paper.abstract.trim()) {
          papersWithAbstracts++;
          // Truncate very long abstracts to avoid token limits
          const truncatedAbstract =
            paper.abstract.length > 500
              ? paper.abstract.substring(0, 500) + "..."
              : paper.abstract;
          datasetSummary += `   Abstract: ${truncatedAbstract}\n`;
        } else {
          datasetSummary += `   Abstract: [Not available]\n`;
        }
        datasetSummary += "\n";
      });

      console.log(
        `Papers with abstracts: ${papersWithAbstracts}/${datasetContext.papers.length}`,
      );
      datasetSummary += "\n";
    }

    console.log("Dataset summary:", datasetSummary);

    // System prompt with guardrails
    const systemPrompt = `You are a research data analyst assistant helping users understand and analyze academic research data from OpenAlex. You have access to the COMPLETE dataset of researchers and/or papers based on the user's query - not just samples, but ALL the data.

DATASET ACCESS:
- You have access to ALL entries in the dataset, not just examples
- Each author/paper entry includes comprehensive metrics and metadata
- For papers: includes abstracts when available, allowing content-based analysis
- You can perform statistical analysis, rankings, comparisons across the entire dataset
- The data includes advanced metrics like citation velocity, trending scores, and venue rankings

GUIDELINES:
1. Focus exclusively on analyzing and discussing the provided research data
2. Provide insights about research trends, author productivity, citation patterns, and academic impact
3. Be factual and base all responses on the actual data provided
4. Suggest meaningful analyses, comparisons, or visualizations the user might find interesting
5. Help identify top performers, emerging trends, or notable patterns in the data
6. Leverage the complete dataset for comprehensive statistical analysis

CAPABILITIES:
- Analyze citation patterns and research impact across ALL entries
- Compare researchers or papers within the complete dataset
- Identify trends in publication dates, venues, or topics
- Analyze paper abstracts for content themes, methodologies, and research focus
- Calculate statistics, percentiles, and distributions
- Suggest data visualizations or further analysis
- Explain research metrics and their significance
- Provide content-based insights using paper abstracts when available

RESTRICTIONS:
1. Do NOT provide advice on non-research topics
2. Do NOT generate fake data or make up information not present in the dataset
3. Do NOT provide personal information about researchers beyond what's in the academic data
4. Do NOT engage in discussions unrelated to research analysis
5. Keep responses focused, concise, and data-driven
6. If asked about topics outside research analysis, politely redirect to the dataset

Remember: You have the COMPLETE dataset at your disposal. Use it to provide comprehensive, data-driven insights.`;

    // Call our server-side API instead of directly accessing AWS
    if (onChunk) {
      // Use streaming
      const response = await fetch("/api/chatbot", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          messages,
          datasetContext,
          streaming: true,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Failed to get response from server",
        );
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("No response body received");
      }

      let fullResponse = "";
      const decoder = new TextDecoder();

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split("\n");

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const data = JSON.parse(line.slice(6));
                if (data.text) {
                  fullResponse += data.text;
                  onChunk(data.text);
                }
              } catch (e) {
                // Ignore malformed JSON
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      return fullResponse;
    } else {
      // Use non-streaming
      const response = await fetch("/api/chatbot", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          messages,
          datasetContext,
          streaming: false,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Failed to get response from server",
        );
      }

      const data = await response.json();
      return data.response;
    }
  } catch (error) {
    console.error("Error calling Bedrock:", error);
    throw new Error(
      `Failed to get response from Claude: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

export async function validateAWSConfig(): Promise<boolean> {
  try {
    // Check server-side AWS configuration via API
    const response = await fetch("/api/chatbot", {
      method: "GET",
    });

    if (!response.ok) {
      return false;
    }

    const data = await response.json();
    return data.configured;
  } catch (error) {
    console.error("AWS configuration validation failed:", error);
    return false;
  }
}
