import React from "react";
import Dashboard from "./components/Dashboard";
import { getUser } from "@/lib/cache/getUser";
import { redirect } from "next/navigation";

export default async function Curie2Page() {
  const user = await getUser();

  if (!user) {
    redirect("/signin");
  }

  if (user.role !== "PAID_USER") {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-gray-50 to-white text-black px-4">
        <div className="max-w-xl text-center">
          <h1 className="text-3xl sm:text-4xl font-extrabold mb-4">
            Access Restricted
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            You need to be a <span className="font-semibold">paid user</span> to
            view this page.
          </p>
          {/* <a
            href="/pricing"
            className="inline-block rounded-xl bg-black text-white px-6 py-3 hover:bg-gray-800 transition"
          >
            Upgrade Your Account
          </a> */}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white text-black">
      <Dashboard />
    </div>
  );
}
