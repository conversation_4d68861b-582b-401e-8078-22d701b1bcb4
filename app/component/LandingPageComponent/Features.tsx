"use client";

import React from "react";
import Image from "next/image";
import stayOnTheCuttingEdge1 from "@/public/StayOnTheCuttingEdge1.png";
import stayOnTheCuttingEdge2 from "@/public/StayOnTheCuttingEdge2.png";
import stayOnTheCuttingEdge3 from "@/public/StayOnTheCuttingEdge3.png";

import type { StaticImageData } from "next/image";

const ContentItem = ({
  imageSrc,
  imageAlt,
  title,
  description,
}: {
  imageSrc: StaticImageData;
  imageAlt: string;
  title: string;
  description: string;
}) => {
  // white to gray gradient linear
  return (
    <div className="flex flex-col items-center text-center rounded-xl shadow-lg lg:px-6 px-4 min-h-72 py-8 w-full max-w-sm my-4 bg-[linear-gradient(to_bottom,#FFFFFF_0%,#FBFEFA_100%)]">
      {/* Icon in a circular background */}
      <div className="w-full items-start justify-start text-start">
        <div className="w-16 h-16 flex items-start justify-start rounded-full bg-[#E6F4EA] mb-4">
          <Image src={imageSrc} alt={imageAlt} className="w-16 h-16" />
        </div>

        {/* Title */}
        <div className="flex h-12  ">
          <h3 className="font-medium text-2xl text-[#1D1E2C] mb-2  ">
            {title}
          </h3>
        </div>

        {/* Description */}
        <p className="text-lg text-[#333] ">{description}</p>
      </div>
    </div>
  );
};

export default function App() {
  const contents = [
    {
      imageSrc: stayOnTheCuttingEdge1,
      imageAlt: "Papers",
      title: "Ask.",
      description: "Answer questions with evidence from scientific literature.",
    },
    {
      imageSrc: stayOnTheCuttingEdge2,
      imageAlt: "Idea",
      title: "Discover.",
      description:
        "Stay updated with latest and trending research papers and researchers.",
    },
    {
      imageSrc: stayOnTheCuttingEdge3,
      imageAlt: "Path",
      title: "Find.",
      description: "Identify institutions and authors to collaborate with.",
    },
  ];

  return (
    <div className="flex flex-col items-center justify-center w-full bg-[#f9fafb] ">
      <h2 className="text-2xl lg:text-4xl font-medium text-center mb-10 text-[#27394F]">
        Why Outread?
      </h2>

      <div className="flex flex-col md:flex-row items-start justify-start h-full max-w-6xl gap-x-2 p-4 w-full">
        {contents.map((content, index) => (
          <ContentItem
            key={index}
            imageSrc={content.imageSrc}
            imageAlt={content.imageAlt}
            title={content.title}
            description={content.description}
          />
        ))}
      </div>
    </div>
  );
}
