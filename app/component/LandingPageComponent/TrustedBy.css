@keyframes infiniteScroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(-100% / 3));
  }
}

.logo-scroll-container {
  display: flex;
  animation: infiniteScroll 30s linear infinite;
  width: fit-content;
}

.logo-item {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 2rem; /* 32px consistent spacing */
}

.logo-item img {
  height: 28px !important; /* Force consistent height */
  width: auto !important; /* Let width adjust to maintain aspect ratio */
  object-fit: contain;
}
