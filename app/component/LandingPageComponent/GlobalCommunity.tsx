"use client";
import React from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";

export const GlobalCommunity = () => {
  const people = [
    {
      src: "Executives.png",
      alt: "Executive",
      title: "Executives",
      description:
        "Stay ahead of the curve with research-backed insights delivered in minutes, powering smarter business and policy moves.",
    },
    {
      src: "Innovation.png",
      alt: "Innovation Leader",
      title: "Innovation Leaders",
      description:
        "Spot emerging trends, scout new technologies, and make faster, evidence-based decisions with confidence.",
    },
    {
      src: "Researchers.png",
      alt: "Researcher",
      title: "Researchers",
      description:
        "Cut through noise and uncover high-impact research, summarized, ranked, and ready for synthesis.",
    },
  ];
  const router = useRouter();

  return (
    <section className="w-full  mt-[-90px] bg-[linear-gradient(to_bottom,_#f9fafb_0%,_rgba(136,216,77,0.575)_30%,_rgba(136,216,77,0.4)_60%,_#f9fafb_100%)] pt-10 px-4 text-center">
      <div className="max-w-7xl mx-auto flex flex-col items-center">
        <h2 className="text-2xl lg:text-4xl font-medium mb-16 max-w-3xl text-[#27394F]">
          Testimonials
          <br /> to grow and stay informed
        </h2>

        {/* Cards */}
        <div className="flex flex-wrap justify-center gap-10 mb-16">
          {people.map((person, index) => (
            <div
              key={index}
              className="bg-transparent rounded-xl overflow-hidden w-[300px] flex flex-col items-center"
            >
              {/* Image */}
              <div className="relative w-full h-[360px]">
                <Image
                  src={person.src}
                  alt={person.alt}
                  fill
                  className="object-cover"
                />
                x
              </div>

              {/* Text */}
              <div className="px-6 py-6 text-left w-full">
                <h3 className="lg:text-2xl text-lg font-semibold mb-2 text-[#27394F]">
                  {person.title}
                </h3>
                <p className="lg:text-lg text-sm text-[#111E2B]">
                  {person.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* CTA */}
        {/* <div
          onClick={() => router.push("/signin")}
          className="bg-[#88D84D] text-black rounded-3xl text-xl px-16 py-4 cursor-pointer hover:bg-[#7BC43D] transition-colors inline-flex items-center justify-center font-medium"
        >
          GET STARTED
        </div> */}
      </div>
    </section>
  );
};
