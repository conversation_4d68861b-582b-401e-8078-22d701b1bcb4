"use client";

import React from "react";
import { Accordion, AccordionItem } from "@nextui-org/react";

const faqs = [
  {
    question: "What can I access?",
    answer: `With a premium subscription to Outread, you gain full access to DarwinAI and CurieAI our powerful tools for research discovery and insight generation.
DarwinAI: Ask any research question and get science-backed, source-cited answers based on 300M+ academic papers.


CurieAI: Explore the most novel and fast-growing research topics using keywords across disciplines, ranked by originality and momentum.


Subscribers can generate unlimited insights, download reports, and explore new research without the noise.
Plans are $20/month or $120/year, with 5 free usage tokens to try it out before subscribing. Cancel anytime.`,
  },
  {
    question: "Is this right for me?",
    answer: `Outread is an AI-powered research intelligence platform built for professionals, analysts, and curious minds who need fast, reliable insights from academic research. It helps you cut through the noise of over 5 million research papers published each year by highlighting the most novel and high-impact research through CurieAI and answering deep questions with science-backed insights through DarwinAI. If you work in R&D, consulting, policy, or simply want to stay ahead of emerging science without spending hours reading PDFs, Outread is designed for you.
`,
  },
  {
    question: "How are we different?",
    answer:
      "Outread covers a wide range of academic papers across various industries, focusing on the most influential and impactful research. Using multiple metrics such as Altmetric scores, Bibliometrics, i-10 author index scores, and H-index, we curate and present breakthrough papers to ensure you have access to the most significant and cutting-edge research available. Our goal is to filter through the noise and deliver the most relevant and high-quality papers to our audience.",
  },
];

export default function FaqSection() {
  return (
    <section className="w-full bg-white text-white mt-[-50px] h-[100px] lg:h-[200px] bg-[url('/curvedBGFlipped.png')] bg-cover">
      {/* <div className="w-full  text-center pt-[150px]">
        <h2 className="text-2xl lg:text-3xl font-semibold mb-10">
          Frequently Asked Questions
        </h2>

        <div className=" w-full flex justify-start items-center flex-col">
          <Accordion className="text-white lg:w-[1300px] w-[350px] mb-2 text-left">
            {faqs.map((faq, index) => (
              <AccordionItem
                key={index}
                aria-label={`FAQ ${index}`}
                title={faq.question}
                classNames={{
                  title: "text-[white] text-start",
                  content: "text-[white]",
                  base: "border-b border-white/30",
                }}
              >
                {faq.answer}
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div> */}
    </section>
  );
}
