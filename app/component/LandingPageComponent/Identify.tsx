"use client";

import Image from "next/image";
import curie from "@/public/curie.png";
export default function IdentifyEmergingThemesSection() {
  return (
    <section className="w-full px-4 bg-[#f9fafb]">
      <div className="max-w-7xl mx-auto text-center">
        {/* Heading */}
        <h2 className="text-2xl lg:text-4xl flex font-medium justify-center mb-6">
          <div className="text-center w-full lg:w-[60%] text-[#27394F]">
            See what’s trending before the rest of the world does
          </div>
        </h2>

        {/* Card with gradient + image */}
        <div className="mt-12 rounded-2xl overflow-hidden">
          <div className="rounded-xl overflow-hidden">
            <Image
              src={curie}
              alt="Identify Emerging Themes"
              width={1400}
              height={800}
              className="w-full h-auto object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
