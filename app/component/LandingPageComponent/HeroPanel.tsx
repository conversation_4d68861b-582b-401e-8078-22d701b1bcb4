"use client";

import Image from "next/image";
import hero from "@/public/Hero Image.png";
import { useRouter } from "next/navigation";

export default function HeroPanel() {
  const router = useRouter();

  return (
    <div className="relative flex justify-center w-full lg:h-[1000px] h-[600px] aspect-[1440/1200] text-white bg-[url('/Background.png')] bg-cover bg-bottom">
      {" "}
      {/* Dark overlay */}
      <div className="absolute inset-0  z-10" />
      {/* Foreground Content */}
      <div className="relative z-20 flex flex-col items-center justify-center text-center h-full px-4">
        <h1 className="text-3xl md:text-4xl  mb-4">Discover Insights from</h1>
        <p className="text-xl md:text-4xl font-semibold italic mb-6">
          <span className="font-medium">250M+ Research Papers</span>
        </p>

        <div
          onClick={() => router.push("/signin")}
          className="bg-[#88D84D] text-black rounded-3xl text-xl px-12 py-3 cursor-pointer hover:bg-[#7BC43D] transition-colors inline-flex items-center justify-center font-medium"
        >
          GET STARTED
        </div>

        {/* Hero Image */}
        <div className="mt-10 max-w-[1100px] w-full">
          <Image
            src={hero}
            alt="Hero Image"
            width={1200}
            height={600}
            className="w-full h-auto mx-auto"
          />
        </div>
      </div>
    </div>
  );
}
