import Image from "next/image";
import "./TrustedBy.css";
export const TrustedBy = () => {
  const logos = [
    { src: "/Google Blue Logo.png", alt: "Google" },
    { src: "/AWS Blue Logo.png", alt: "AWS" },
    { src: "/Techstars Blue Logo.png", alt: "Techstars" },
    { src: "/Zoho Blue Logo.png", alt: "Zoho" },
    { src: "/LinkedIn Blue Logo.png", alt: "LinkedIn" },
    { src: "/Microsoft Blue Logo.png", alt: "Microsoft" },
    { src: "/Dell Blue Logo.png", alt: "Dell" },
    { src: "/fortescue.png", alt: "Fortescue" },
  ];

  return (
    <div className=" -mt-[120px] mx-auto  relative z-30 w-screen flex items-center justify-center">
      <div className="w-full flex flex-col items-center justify-center">
        <h3 className="text-2xl  text-[#27394F] font-medium lg:text-4xl my-12 lg:my-16">
          Trusted by professionals at
        </h3>
        <div className="relative w-full overflow-hidden">
          <div className="logo-scroll-container">
            {/* Create multiple sets for seamless infinite scroll */}
            {Array.from({ length: 3 }, (_, setIndex) =>
              logos.map((img, index) => (
                <div key={`set-${setIndex}-${index}`} className="logo-item">
                  <Image
                    src={img.src}
                    alt={img.alt}
                    width={15}
                    height={60}
                    className="logo-image"
                  />
                </div>
              )),
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
