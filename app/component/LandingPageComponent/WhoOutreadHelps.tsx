"use client";

import Image from "next/image";
import React from "react";

const roles = [
  {
    icon: "/Icon - Consulting & Strategy Teams.png",
    title: "Consulting",
    description:
      "Surface trends, and insights from more than 250M+ research papers to accelerate product innovation.",
  },
  {
    icon: "/Icon - R&D & Innovation.png",
    title: "R&D & Innovation Units",
    description:
      "Stay updated with the current research breakthroughs, patents and themes in your industry.",
  },

  {
    icon: "/Icon - Journalists & Writers.png",
    title: "Investment",
    description:
      "Discover hotspots of researchers and institutions to partner with or commercialise technologies.",
  },
];

export const WhoWeHelp = () => {
  return (
    <div className="w-full bg-[#f9fafb] pb-20 px-4 text-[#27394F]">
      <div className="max-w-7xl mx-auto text-center">
        <h2 className="text-2xl lg:text-4xl font-medium mb-16">Use Cases</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {roles.map((role, index) => (
            <div
              key={index}
              className="bg-white rounded-2xl border border-gray-200 shadow-xl p-8 flex flex-col items-start text-left"
            >
              <div className="w-16 h-16 rounded-2xl bg-white flex items-center justify-center mb-6">
                <Image
                  src={role.icon}
                  alt={role.title}
                  width={64}
                  height={64}
                  className="object-contain"
                />
              </div>
              <h3 className="text-xl font-semibold mb-4 text-gray-900">
                {role.title}
              </h3>
              <p className="text-sm text-gray-600 leading-relaxed">
                {role.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
