"use client";

import Image, { StaticImageData } from "next/image";
import { useRouter } from "next/navigation";
import darwin from "@/public/darwin.png";
import curie from "@/public/curie.png";

const FeatureCard = ({
  imageSrc,
  title,
  subtitle,
  description,
  reverse = false,
}: {
  imageSrc: StaticImageData;
  title: string;
  subtitle: string;
  description: string;
  reverse?: boolean;
}) => {
  return (
    <div
      className={`flex flex-col md:flex-row ${
        reverse ? "md:flex-row-reverse" : ""
      } items-center justify-between gap-6 mb-12`}
    >
      {/* Image */}
      <div className="flex-1 w-full">
        <div className="rounded-2xl overflow-hidden shadow-lg">
          <Image
            src={imageSrc}
            alt={title}
            width={600}
            height={400}
            className="w-full h-auto"
          />
        </div>
      </div>

      {/* Text */}
      <div className="flex-1 w-full px-2">
        <div className="flex lg:flex-row lg:items-center flex-col h-full gap-3 items-start">
          {/* Text block */}
          {/* Accent line */}
          <div
            className="
        w-48 h-[3px] bg-gradient-to-r from-[#A8D84D] to-[#88D84D] rounded-sm
        lg:w-[15px] lg:h-48 lg:bg-gradient-to-b lg:order-none
        order-2 mx-4
      "
          />
          <div className="order-1 lg:order-none">
            <h3 className="text-2xl lg:text-4xl  mb-1 text-[##222222]">
              {title}
            </h3>
            <p className="text-sm lg:text-lg font-medium text-[#041527] mb-2">
              {subtitle}
            </p>
            <p className="text-sm lg:text-lg text-[#041527]">{description}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function FeatureToolsSection() {
  const router = useRouter();

  return (
    <section className="w-full lg:w-[1300px] mx-auto px-4 md:px-20 bg-[#f9fafb]">
      {/* Heading */}
      <h2 className="text-2xl lg:text-4xl font-medium text-center mb-16 text-[#111E2B]">
        Explore research like never before with our powerful tools
      </h2>

      {/* Features */}
      <FeatureCard
        imageSrc={curie}
        title="CurieAI"
        subtitle="Track emerging research trends before they go mainstream."
        description="CurieAI analyses a dataset of over 250 million research papers to spotlight fast-growing topics, novel breakthroughs, and future-shaping ideas."
      />

      <FeatureCard
        imageSrc={darwin}
        title="DarwinAI"
        subtitle="Ask questions. Get evidence-backed answers."
        description="DarwinAI links your questions to the latest peer-reviewed research using our proprietary academic engine."
        reverse
      />

      {/* CTA Button */}
      {/* <div className="flex justify-center mt-[60px]">
        <div
          onClick={() => router.push("/signin")}
          className="bg-[#88D84D] text-black rounded-3xl text-xl px-16 py-4 cursor-pointer hover:bg-[#7BC43D] transition-colors inline-flex items-center justify-center font-medium"
        >
          GET STARTED
        </div>
      </div> */}
    </section>
  );
}

// Fix gap for currieAI
