"use client";

import { Input, Textarea } from "@nextui-org/react";
import { useRef, useState } from "react";
import toast from "react-hot-toast";
// import { GoogleReCaptchaProvider, useGoogleReCaptcha } from "react-google-recaptcha-v3";

const endpoint = process.env.DEV_MODE === "true" ? process.env.LOCALHOST : "";

function ContactForm() {
  const formRef = useRef<HTMLFormElement>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  // const { executeRecaptcha } = useGoogleReCaptcha();

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsSubmitting(true);

    // if (!executeRecaptcha) {
    //     console.log("Execute recaptcha not yet available");
    //     return;
    // }

    try {
      // const token = await executeRecaptcha('contact_form');

      const formData = new FormData(event.currentTarget);
      const name = formData.get("name") as string;
      const email = formData.get("email") as string;
      const message = formData.get("message") as string;

      const response = await fetch(endpoint + "/api/sendEmail", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: "contact",
          name,
          email,
          message,
          // recaptchaToken: token
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(data.message || "Message sent successfully!");
        formRef.current?.reset();
      } else {
        toast.error(data.error || "Failed to send message. Please try again.");
      }
    } catch (error) {
      toast.error("An error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form
      ref={formRef}
      onSubmit={handleSubmit}
      className="w-full text-white contact-form"
    >
      <Input
        className="mb-4"
        type="text"
        name="name"
        label="Name"
        placeholder="Enter your name"
        variant="bordered"
        classNames={{
          input: "!text-white placeholder:!text-gray-400",
          label: "!text-white",
          inputWrapper: "data-[hover=true]:border-gray-400",
        }}
        style={{
          color: "white !important",
        }}
        required
      />
      <Input
        className="mb-4"
        type="email"
        name="email"
        label="Email"
        placeholder="Enter your email"
        variant="bordered"
        classNames={{
          input: "!text-white placeholder:!text-gray-400",
          label: "!text-white",
          inputWrapper: "data-[hover=true]:border-gray-400",
        }}
        style={{
          color: "white !important",
        }}
        required
      />
      <Textarea
        className="mb-4"
        name="message"
        label="Message"
        placeholder="Enter your message"
        variant="bordered"
        classNames={{
          input: "!text-white placeholder:!text-gray-400",
          label: "!text-white",
          inputWrapper: "data-[hover=true]:border-gray-400",
        }}
        style={{
          color: "white !important",
        }}
        required
      />
      {/* <GoogleReCaptchaProvider reCaptchaKey="6Lce-VsqAAAAAPisNY3absGuWg5fI5Xx0QqQYq-Z"> */}
      <button
        className="w-full bg-[#88D84D] rounded-xl text-black font-semibold py-3 hover:bg-[#7BC43D] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        type="submit"
        disabled={isSubmitting}
      >
        {isSubmitting ? "Sending..." : "Send Message"}
      </button>
      <div className="text-center mt-4 text-white">
        <p>
          or email us at{" "}
          <a
            href="mailto:<EMAIL>"
            className="text-[#88D84D] hover:text-[#7BC43D] transition-colors"
          >
            <EMAIL>
          </a>
        </p>
      </div>
      {/* </GoogleReCaptchaProvider> */}
    </form>
  );
}

export default function ContactUs() {
  return (
    <div className="flex flex-col w-full min-h-[1100px] items-center justify-start pt-20 bg-[#132435]">
      <div className="w-[90%] max-w-[800px] items-center justify-center p-4">
        <h1 className="text-4xl font-semibold text-center text-white mb-8">
          Contact Us
        </h1>
        <ContactForm />
      </div>
    </div>
  );
}
