"use client";

import React, { useRef, useState, useEffect } from "react";
import { Input } from "@nextui-org/react";
import { useFormState, useFormStatus } from "react-dom";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import { newsletterSignup } from "@/app/newsletter/action"; // adjust import path if needed

const endpoint = process.env.DEV_MODE === "true" ? process.env.LOCALHOST : "";

interface NewsletterResult {
  error?: string;
  success?: string;
}

function SubmitButton() {
  const { pending } = useFormStatus();
  return (
    <button
      type="submit"
      disabled={pending}
      className="w-full font-semibold text-black bg-[#88D84D] rounded-xl h-14 hover:bg-[#7BC43D] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
    >
      {pending ? "Signing up..." : "Sign up"}
    </button>
  );
}

export default function NewsletterPage() {
  const [email, setEmail] = useState("");
  const formRef = useRef<HTMLFormElement>(null);
  const [state, formAction] = useFormState<NewsletterResult | null, FormData>(
    newsletterSignup,
    null,
  );
  const router = useRouter();

  async function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault();

    try {
      const response = await fetch(endpoint + `/api/sendEmail`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ type: "newsletter", email }),
      });

      if (!response.ok) throw new Error("Failed to send email");

      const formData = new FormData();
      formData.append("email", email);
      formAction(formData);
    } catch (error) {
      toast.error("Failed to send email");
    }
  }

  useEffect(() => {
    if (state?.error) {
      toast.error(state.error);
      formRef.current?.reset();
    }
    if (state?.success) {
      toast.success(state.success);
      setTimeout(() => {
        router.push("/");
      }, 2000);
      formRef.current?.reset();
    }
  }, [state, router]);

  return (
    <div className="min-h-screen w-full bg-[#132435] flex justify-center items-center px-4">
      <div className="max-w-4xl py-20 w-full bg-[#36434F] text-white text-center flex flex-col items-center">
        <h1 className="text-3xl md:text-4xl font-semibold mb-4">
          Sign up for our newsletter
        </h1>
        <p className="text-sm md:text-base text-gray-300 mb-8">
          Join thousands of professionals, researchers, and innovators using
          Outread to cut through the noise. Get insights, fast-rising research
          trends, and updates.
        </p>

        <form
          ref={formRef}
          onSubmit={handleSubmit}
          className="flex flex-col gap-4 max-w-2xl items-center w-full contact-form"
        >
          <Input
            type="email"
            name="email"
            id="email"
            variant="bordered"
            label="Email"
            placeholder="Enter your email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            classNames={{
              label: "text-white",
              input: "text-white placeholder-white",
              inputWrapper: "bg-white text-black",
            }}
            className="w-full"
          />
          <SubmitButton />
        </form>
      </div>
    </div>
  );
}
